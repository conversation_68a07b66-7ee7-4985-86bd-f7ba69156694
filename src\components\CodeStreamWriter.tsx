import React, { useState, useEffect, useRef } from 'react';

interface CodeStreamWriterProps {
  content: string;
  language: string;
  fileName: string;
  onComplete?: () => void;
  speed?: number; // characters per second
  className?: string;
  showCursor?: boolean;
  autoStart?: boolean;
}

export const CodeStreamWriter: React.FC<CodeStreamWriterProps> = ({
  content,
  language,
  fileName,
  onComplete,
  speed = 50,
  className = "",
  showCursor = true,
  autoStart = true
}) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [isWriting, setIsWriting] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showBlinkingCursor, setShowBlinkingCursor] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const cursorIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Start cursor blinking animation
  useEffect(() => {
    if (showCursor) {
      cursorIntervalRef.current = setInterval(() => {
        setShowBlinkingCursor(prev => !prev);
      }, 500);
    }

    return () => {
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
      }
    };
  }, [showCursor]);

  // Auto-start writing
  useEffect(() => {
    if (autoStart && content && !isWriting) {
      startWriting();
    }
  }, [autoStart, content]);

  const startWriting = () => {
    if (isWriting) return;
    
    setIsWriting(true);
    setCurrentIndex(0);
    setDisplayedContent('');

    const writeCharacter = () => {
      setCurrentIndex(prevIndex => {
        const newIndex = prevIndex + 1;
        
        if (newIndex <= content.length) {
          setDisplayedContent(content.substring(0, newIndex));
          
          // Add realistic pauses
          const currentChar = content[newIndex - 1];
          const nextChar = content[newIndex];
          
          let delay = 1000 / speed; // Base delay
          
          // Longer pauses for certain characters
          if (currentChar === '\n') {
            delay *= 3; // Pause at line breaks
          } else if (currentChar === ';' || currentChar === '{' || currentChar === '}') {
            delay *= 2; // Pause at statement endings
          } else if (currentChar === ' ' && nextChar === '/') {
            delay *= 4; // Pause before comments
          }
          
          if (newIndex < content.length) {
            intervalRef.current = setTimeout(writeCharacter, delay);
          } else {
            // Writing complete
            setIsWriting(false);
            onComplete?.();
          }
        }
        
        return newIndex;
      });
    };

    writeCharacter();
  };

  const stopWriting = () => {
    if (intervalRef.current) {
      clearTimeout(intervalRef.current);
      intervalRef.current = null;
    }
    setIsWriting(false);
  };

  const resetWriting = () => {
    stopWriting();
    setCurrentIndex(0);
    setDisplayedContent('');
  };

  const skipToEnd = () => {
    stopWriting();
    setDisplayedContent(content);
    setCurrentIndex(content.length);
    setIsWriting(false);
    onComplete?.();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
      }
    };
  }, []);

  const getLanguageFromFileName = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'tsx':
      case 'jsx':
        return 'tsx';
      case 'ts':
        return 'typescript';
      case 'js':
        return 'javascript';
      case 'css':
        return 'css';
      case 'html':
        return 'html';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      default:
        return language || 'text';
    }
  };

  const displayLanguage = getLanguageFromFileName(fileName);
  const contentWithCursor = showCursor && isWriting && showBlinkingCursor 
    ? displayedContent + '|' 
    : displayedContent;

  return (
    <div className={`relative ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between bg-gray-800 text-white px-4 py-2 rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <span className="text-sm font-medium ml-2">{fileName}</span>
          <span className="text-xs text-gray-400">({displayLanguage})</span>
        </div>
        
        <div className="flex items-center gap-2">
          {isWriting && (
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              Writing...
            </div>
          )}
          
          <div className="flex gap-1">
            {!isWriting && currentIndex < content.length && (
              <button
                onClick={startWriting}
                className="px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 rounded transition-colors"
              >
                Start
              </button>
            )}
            
            {isWriting && (
              <>
                <button
                  onClick={stopWriting}
                  className="px-2 py-1 text-xs bg-red-600 hover:bg-red-700 rounded transition-colors"
                >
                  Pause
                </button>
                <button
                  onClick={skipToEnd}
                  className="px-2 py-1 text-xs bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                >
                  Skip
                </button>
              </>
            )}
            
            {!isWriting && currentIndex > 0 && (
              <button
                onClick={resetWriting}
                className="px-2 py-1 text-xs bg-gray-600 hover:bg-gray-700 rounded transition-colors"
              >
                Reset
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Code Content */}
      <div className="relative">
        <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-auto text-sm font-mono leading-relaxed">
          <code>{contentWithCursor}</code>
        </pre>
        
        {/* Progress indicator */}
        {content.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-700">
            <div 
              className="h-full bg-blue-500 transition-all duration-100"
              style={{ width: `${(currentIndex / content.length) * 100}%` }}
            />
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="flex justify-between items-center text-xs text-gray-500 mt-2 px-2">
        <span>
          {currentIndex} / {content.length} characters
        </span>
        <span>
          {Math.round((currentIndex / content.length) * 100) || 0}% complete
        </span>
        <span>
          Speed: {speed} chars/sec
        </span>
      </div>
    </div>
  );
};

// Hook for managing multiple code streams
export const useCodeStreamManager = () => {
  const [activeStreams, setActiveStreams] = useState<Map<string, boolean>>(new Map());
  const [completedStreams, setCompletedStreams] = useState<Set<string>>(new Set());

  const startStream = (streamId: string) => {
    setActiveStreams(prev => new Map(prev).set(streamId, true));
  };

  const completeStream = (streamId: string) => {
    setActiveStreams(prev => {
      const newMap = new Map(prev);
      newMap.delete(streamId);
      return newMap;
    });
    setCompletedStreams(prev => new Set(prev).add(streamId));
  };

  const resetStreams = () => {
    setActiveStreams(new Map());
    setCompletedStreams(new Set());
  };

  return {
    activeStreams,
    completedStreams,
    startStream,
    completeStream,
    resetStreams,
    isStreamActive: (streamId: string) => activeStreams.has(streamId),
    isStreamCompleted: (streamId: string) => completedStreams.has(streamId)
  };
};

// Predefined code templates for common files
export const codeTemplates = {
  'package.json': (projectName: string) => `{
  "name": "${projectName.toLowerCase().replace(/\s+/g, '-')}",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "lucide-react": "^0.263.1"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "postcss": "^8.4.27",
    "tailwindcss": "^3.3.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5"
  }
}`,

  'tsconfig.json': () => `{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}`,

  'vite.config.ts': () => `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})`,

  'tailwind.config.js': () => `/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
}`
};
