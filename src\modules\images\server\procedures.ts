import { z } from 'zod';
import { createTRPCRouter, protectedProcedure, baseProcedure } from '@/trpc/init';
import { unsplashService, ImageSearchOptions } from '@/lib/unsplash';
import { unsplashPatterns, ImageContext } from '@/lib/unsplash-patterns';
import { TRPCError } from '@trpc/server';

// Input validation schemas
const imageSearchSchema = z.object({
  query: z.string().min(1).max(100),
  orientation: z.enum(['landscape', 'portrait', 'squarish']).optional(),
  color: z.enum(['black_and_white', 'black', 'white', 'yellow', 'orange', 'red', 'purple', 'magenta', 'green', 'teal', 'blue']).optional(),
  per_page: z.number().min(1).max(30).default(10),
  page: z.number().min(1).default(1),
});

const imageContextSchema = z.object({
  type: z.enum(['project', 'landing', 'error', 'profile', 'fragment', 'placeholder']),
  content: z.string().optional(),
  category: z.string().optional(),
  mood: z.enum(['professional', 'creative', 'minimal', 'vibrant', 'dark', 'light']).optional(),
  aspectRatio: z.enum(['square', 'landscape', 'portrait']).optional(),
});

const imageByIdSchema = z.object({
  id: z.string().min(1),
});

const trackDownloadSchema = z.object({
  downloadLocation: z.string().url(),
});

const projectImageSchema = z.object({
  projectName: z.string().min(1).max(100),
  content: z.string().optional(),
});

const landingImageSchema = z.object({
  theme: z.enum(['light', 'dark']).default('light'),
});

const errorImageSchema = z.object({
  errorType: z.enum(['404', '500', 'general']).default('general'),
});

const profileImageSchema = z.object({
  username: z.string().optional(),
});

const fragmentImageSchema = z.object({
  content: z.string().min(1),
  title: z.string().optional(),
});

const aiContentImagesSchema = z.object({
  prompt: z.string().min(1).max(200),
  count: z.number().min(1).max(10).default(3),
});

const themedCollectionSchema = z.object({
  theme: z.string().min(1).max(50),
  count: z.number().min(1).max(10).default(5),
});

export const imagesRouter = createTRPCRouter({
  /**
   * Search for images by query
   */
  search: baseProcedure
    .input(imageSearchSchema)
    .query(async ({ input }) => {
      try {
        const options: ImageSearchOptions = {
          orientation: input.orientation,
          color: input.color,
          per_page: input.per_page,
          page: input.page,
        };

        const images = await unsplashService.searchImages(input.query, options);
        
        return {
          images,
          query: input.query,
          total: images.length,
          page: input.page,
          per_page: input.per_page,
        };
      } catch (error) {
        console.error('Error searching images:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to search images',
        });
      }
    }),

  /**
   * Get contextual image based on context
   */
  contextual: baseProcedure
    .input(imageContextSchema)
    .query(async ({ input }) => {
      try {
        const context: ImageContext = {
          type: input.type,
          content: input.content,
          category: input.category,
          mood: input.mood,
          aspectRatio: input.aspectRatio,
        };

        const image = await unsplashPatterns.getContextualImage(context);
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable image found for the given context',
          });
        }

        return {
          image,
          context,
        };
      } catch (error) {
        console.error('Error getting contextual image:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get contextual image',
        });
      }
    }),

  /**
   * Get random image by category
   */
  random: baseProcedure
    .input(z.object({
      category: z.string().optional(),
      orientation: z.enum(['landscape', 'portrait', 'squarish']).optional(),
    }))
    .query(async ({ input }) => {
      try {
        const image = await unsplashService.getRandomImage(input.category, {
          orientation: input.orientation,
        });
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No random image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting random image:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get random image',
        });
      }
    }),

  /**
   * Get image by ID
   */
  byId: baseProcedure
    .input(imageByIdSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashService.getImageById(input.id);
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Image not found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting image by ID:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get image',
        });
      }
    }),

  /**
   * Track image download (for Unsplash analytics)
   */
  trackDownload: baseProcedure
    .input(trackDownloadSchema)
    .mutation(async ({ input }) => {
      try {
        await unsplashService.trackDownload(input.downloadLocation);
        return { success: true };
      } catch (error) {
        console.error('Error tracking download:', error);
        // Don't throw error for tracking failures
        return { success: false };
      }
    }),

  /**
   * Specialized endpoints for common use cases
   */

  // Get project thumbnail
  projectThumbnail: protectedProcedure
    .input(projectImageSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashPatterns.getProjectThumbnail(
          input.projectName,
          input.content
        );
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable project image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting project thumbnail:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get project thumbnail',
        });
      }
    }),

  // Get landing page hero image
  landingHero: baseProcedure
    .input(landingImageSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashPatterns.getLandingHeroImage(input.theme);
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable landing image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting landing hero image:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get landing hero image',
        });
      }
    }),

  // Get error page background
  errorBackground: baseProcedure
    .input(errorImageSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashPatterns.getErrorPageImage(input.errorType);
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable error image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting error background:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get error background',
        });
      }
    }),

  // Get profile placeholder
  profilePlaceholder: protectedProcedure
    .input(profileImageSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashPatterns.getProfilePlaceholder(input.username);
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable profile image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting profile placeholder:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get profile placeholder',
        });
      }
    }),

  // Get fragment preview image
  fragmentPreview: protectedProcedure
    .input(fragmentImageSchema)
    .query(async ({ input }) => {
      try {
        const image = await unsplashPatterns.getFragmentPreview(
          input.content,
          input.title
        );
        
        if (!image) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'No suitable fragment image found',
          });
        }

        return { image };
      } catch (error) {
        console.error('Error getting fragment preview:', error);
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get fragment preview',
        });
      }
    }),

  // Get AI content images
  aiContentImages: protectedProcedure
    .input(aiContentImagesSchema)
    .query(async ({ input }) => {
      try {
        const images = await unsplashPatterns.getAIContentImages(
          input.prompt,
          input.count
        );
        
        return {
          images,
          prompt: input.prompt,
          count: images.length,
        };
      } catch (error) {
        console.error('Error getting AI content images:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get AI content images',
        });
      }
    }),

  // Get themed collection
  themedCollection: baseProcedure
    .input(themedCollectionSchema)
    .query(async ({ input }) => {
      try {
        const images = await unsplashPatterns.getThemedCollection(
          input.theme,
          input.count
        );
        
        return {
          images,
          theme: input.theme,
          count: images.length,
        };
      } catch (error) {
        console.error('Error getting themed collection:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get themed collection',
        });
      }
    }),

  // Get cache statistics (for debugging)
  cacheStats: protectedProcedure
    .query(async () => {
      try {
        const stats = unsplashService.getCacheStats();
        return stats;
      } catch (error) {
        console.error('Error getting cache stats:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get cache statistics',
        });
      }
    }),

  // Clear cache (for debugging)
  clearCache: protectedProcedure
    .mutation(async () => {
      try {
        unsplashService.clearCache();
        return { success: true };
      } catch (error) {
        console.error('Error clearing cache:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to clear cache',
        });
      }
    }),
});
