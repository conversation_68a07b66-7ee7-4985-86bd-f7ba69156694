# 🖼️ Unsplash Integration Guide

## Overview

This comprehensive Unsplash integration provides intelligent image selection, caching, and contextual recommendations for the Vibe application. The integration includes smart image patterns, React components, tRPC endpoints, and custom hooks for seamless image management.

## 🚀 Features

### ✨ Core Features
- **Intelligent Image Selection**: Context-aware image recommendations
- **Advanced Caching**: In-memory caching with TTL and rate limiting
- **Smart Fallbacks**: Graceful degradation when API limits are reached
- **Proper Attribution**: Automatic Unsplash attribution compliance
- **Type Safety**: Full TypeScript support throughout
- **Performance Optimized**: Lazy loading, image optimization, and caching

### 🎯 Specialized Use Cases
- **Project Thumbnails**: Dynamic thumbnails based on project names and content
- **Landing Hero Images**: Themed hero images for landing pages
- **Error Page Backgrounds**: Calming images for error states
- **Profile Placeholders**: Abstract images for user avatars
- **Fragment Previews**: Contextual images for generated code fragments
- **AI Content Images**: Images selected based on AI prompts

## 📁 File Structure

```
src/
├── lib/
│   ├── unsplash.ts              # Core Unsplash service
│   └── unsplash-patterns.ts     # Intelligent image selection patterns
├── components/
│   └── unsplash-image.tsx       # React components for image display
├── modules/images/server/
│   └── procedures.ts            # tRPC endpoints for image operations
├── hooks/
│   └── use-unsplash.ts          # React hooks for easy integration
└── app/(home)/demo/
    └── page.tsx                 # Comprehensive demo page
```

## 🔧 Setup & Configuration

### 1. API Credentials
The integration uses the provided Unsplash API credentials:
- **Access Key**: `*******************************************`
- **Secret Key**: `c_0iAuikS4JKIbmqGTrmmeOia4xpGU_H7SabbHyn60M`

### 2. Dependencies
```bash
npm install unsplash-js
```

### 3. tRPC Integration
The images router is automatically added to the main tRPC app router:
```typescript
export const appRouter = createTRPCRouter({
  usage: usageRouter,
  messages: messagesRouter,
  projects: projectsRouter,
  images: imagesRouter, // ✅ Added
});
```

## 🎨 Usage Examples

### Basic Image Display
```tsx
import { UnsplashImage } from '@/components/unsplash-image';

<UnsplashImage
  query="technology"
  width={400}
  height={300}
  className="rounded-lg"
/>
```

### Contextual Image Selection
```tsx
<UnsplashImage
  context={{
    type: 'project',
    content: 'React dashboard application',
    mood: 'professional',
    aspectRatio: 'landscape',
  }}
  width={400}
  height={300}
/>
```

### Specialized Components
```tsx
import { 
  ProjectThumbnail, 
  LandingHeroImage, 
  ProfilePlaceholder 
} from '@/components/unsplash-image';

// Project thumbnail
<ProjectThumbnail
  projectName="E-commerce App"
  content="Modern shopping platform"
  width={200}
  height={150}
/>

// Landing hero
<LandingHeroImage
  theme="dark"
  width={1920}
  height={1080}
  className="w-full h-full"
/>

// Profile placeholder
<ProfilePlaceholder
  username="johndoe"
  width={100}
  height={100}
  className="rounded-full"
/>
```

### Using Hooks
```tsx
import { useProjectThumbnail, useAIContentImages } from '@/hooks/use-unsplash';

function ProjectCard({ project }) {
  const { data: thumbnail, isLoading } = useProjectThumbnail(
    project.name,
    project.description
  );

  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <img src={thumbnail?.image.urls.small} alt={project.name} />
      <h3>{project.name}</h3>
    </div>
  );
}
```

## 🔌 tRPC Endpoints

### Search Images
```typescript
const { data } = trpc.images.search.useQuery({
  query: 'technology',
  orientation: 'landscape',
  per_page: 10,
});
```

### Contextual Images
```typescript
const { data } = trpc.images.contextual.useQuery({
  type: 'project',
  content: 'React application',
  mood: 'professional',
});
```

### Specialized Endpoints
```typescript
// Project thumbnail
trpc.images.projectThumbnail.useQuery({
  projectName: 'My App',
  content: 'Description',
});

// AI content images
trpc.images.aiContentImages.useQuery({
  prompt: 'Build a social media app',
  count: 4,
});

// Themed collection
trpc.images.themedCollection.useQuery({
  theme: 'technology',
  count: 6,
});
```

## 🧠 Intelligent Features

### Context-Aware Selection
The system analyzes content to determine the best images:

```typescript
// Automatically detects keywords and mood
const context = {
  type: 'project',
  content: 'dark mode dashboard with analytics',
  // → Automatically sets mood: 'dark', category: 'analytics'
};
```

### Smart Caching
- **In-memory caching** with 1-hour TTL
- **Rate limiting** (50 requests/hour for demo)
- **Intelligent cache keys** based on query parameters
- **Cache statistics** for monitoring

### Fallback Strategy
1. **Cache hit**: Return cached image instantly
2. **API available**: Fetch from Unsplash API
3. **Rate limited**: Use fallback images
4. **Error state**: Display placeholder with graceful degradation

## 🎯 Integration Points

### Current Integrations
1. **Project List** (`src/modules/home/<USER>/components/project-list.tsx`)
   - Dynamic thumbnails for each project
   - Replaces static logo with contextual images

2. **Landing Page** (`src/app/(home)/page.tsx`)
   - Hero background image with theme support
   - Gradient overlay for text readability

3. **Fragment Cards** (`src/modules/projects/server/ui/components/message-card.tsx`)
   - Preview images for generated code fragments
   - Enhanced visual appeal for project previews

### Demo Page
Visit `/demo` to see all features in action:
- Live search with caching
- Contextual image selection
- Specialized components
- AI content integration
- Themed collections
- Cache statistics

## 🔒 Security & Performance

### Rate Limiting
- **50 requests/hour** for demo usage
- **Automatic fallbacks** when limits exceeded
- **Cache-first strategy** to minimize API calls

### Attribution Compliance
- **Automatic attribution** for all images
- **Customizable positioning** (4 corners)
- **Proper UTM tracking** for Unsplash analytics
- **Download tracking** for usage statistics

### Performance Optimizations
- **Lazy loading** by default
- **Image size optimization** (thumb, small, regular, full)
- **Next.js Image component** integration
- **Responsive image selection**

## 🚀 Advanced Usage

### Custom Image Patterns
```typescript
import { unsplashPatterns } from '@/lib/unsplash-patterns';

// Create custom image selection logic
const customImage = await unsplashPatterns.getContextualImage({
  type: 'custom',
  content: 'blockchain cryptocurrency',
  mood: 'futuristic',
  aspectRatio: 'landscape',
});
```

### Batch Image Loading
```typescript
import { useBatchImages } from '@/hooks/use-unsplash';

const { images, isLoading } = useBatchImages([
  'technology',
  'design',
  'development',
]);
```

### Cache Management
```typescript
import { useCacheStats, useClearCache } from '@/hooks/use-unsplash';

const { data: stats } = useCacheStats();
const clearCache = useClearCache();

// Monitor cache performance
console.log(`Cache size: ${stats.size}, Keys: ${stats.keys}`);

// Clear cache when needed
await clearCache.mutateAsync();
```

## 🔮 Future Enhancements

### Planned Features
- **Redis caching** for production environments
- **Image preprocessing** and optimization
- **Advanced analytics** and usage tracking
- **Custom image collections** and favorites
- **AI-powered image tagging** and categorization
- **Progressive image loading** with blur-up effect

### Scalability Considerations
- **CDN integration** for faster image delivery
- **Database caching** for persistent storage
- **Background image prefetching** for common queries
- **Image compression** and format optimization
- **Webhook integration** for real-time updates

## 📊 Monitoring & Analytics

### Available Metrics
- Cache hit/miss ratios
- API request counts
- Image load performance
- User interaction tracking
- Error rates and fallback usage

### Debug Tools
- Cache statistics endpoint
- Image loading state indicators
- Error boundary integration
- Performance monitoring hooks

---

## 🎉 Getting Started

1. **Install dependencies**: `npm install unsplash-js`
2. **Import components**: Use pre-built components or hooks
3. **Visit demo page**: Go to `/demo` to explore features
4. **Integrate gradually**: Start with project thumbnails, expand to other areas
5. **Monitor performance**: Use cache stats and debug tools

The integration is designed to be **plug-and-play** while providing extensive customization options for advanced use cases.
