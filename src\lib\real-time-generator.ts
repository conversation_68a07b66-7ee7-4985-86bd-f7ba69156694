"use client";

import { EventEmitter } from 'events';

export interface FileGenerationEvent {
  type: 'file_start' | 'file_progress' | 'file_complete';
  fileName: string;
  content?: string;
  partialContent?: string;
  progress?: number;
  timestamp: number;
}

export interface ProjectFile {
  name: string;
  path: string;
  content: string;
  type: 'component' | 'config' | 'package' | 'style' | 'image';
}

class RealTimeGenerator extends EventEmitter {
  private isGenerating = false;
  private currentFiles: ProjectFile[] = [];

  constructor() {
    super();
  }

  async generateProject(prompt: string): Promise<void> {
    if (this.isGenerating) return;
    
    this.isGenerating = true;
    this.currentFiles = [];

    // Generate files based on project type
    const files = this.createProjectFiles(prompt);
    
    // First, emit all files as pending to show the queue
    this.emit('files_queued', files.map(f => ({ 
      name: f.name, 
      path: f.path, 
      type: f.type,
      status: 'pending' 
    })));
    
    // Generate each file with streaming effect
    for (const file of files) {
      await this.generateFileWithStreaming(file);
      await new Promise(resolve => setTimeout(resolve, 800)); // Brief pause between files
    }

    this.isGenerating = false;
    this.emit('generation_complete', this.currentFiles);
  }

  private createProjectFiles(prompt: string): ProjectFile[] {
    const projectType = this.detectProjectType(prompt);
    const files: ProjectFile[] = [];

    // Always start with package.json
    files.push({
      name: 'package.json',
      path: 'package.json',
      type: 'package',
      content: this.generatePackageJson(projectType)
    });

    // Add configuration files
    files.push({
      name: 'tailwind.config.js',
      path: 'tailwind.config.js',
      type: 'config',
      content: this.generateTailwindConfig()
    });

    files.push({
      name: 'next.config.js',
      path: 'next.config.js', 
      type: 'config',
      content: this.generateNextConfig()
    });

    // Add main components based on project type
    if (projectType === 'ecommerce') {
      files.push(
        {
          name: 'app/page.tsx',
          path: 'app/page.tsx',
          type: 'component',
          content: this.generateEcommercePage()
        },
        {
          name: 'components/ProductCard.tsx',
          path: 'components/ProductCard.tsx',
          type: 'component',
          content: this.generateProductCard()
        },
        {
          name: 'components/ShoppingCart.tsx',
          path: 'components/ShoppingCart.tsx',
          type: 'component',
          content: this.generateShoppingCart()
        },
        {
          name: 'components/Header.tsx',
          path: 'components/Header.tsx',
          type: 'component',
          content: this.generateHeader()
        }
      );
    } else if (projectType === 'dashboard') {
      files.push(
        {
          name: 'app/page.tsx',
          path: 'app/page.tsx',
          type: 'component',
          content: this.generateDashboardPage()
        },
        {
          name: 'components/Sidebar.tsx',
          path: 'components/Sidebar.tsx',
          type: 'component',
          content: this.generateSidebar()
        },
        {
          name: 'components/MetricsCard.tsx',
          path: 'components/MetricsCard.tsx',
          type: 'component',
          content: this.generateMetricsCard()
        }
      );
    } else {
      // Generic app
      files.push(
        {
          name: 'app/page.tsx',
          path: 'app/page.tsx',
          type: 'component',
          content: this.generateGenericPage()
        },
        {
          name: 'components/Layout.tsx',
          path: 'components/Layout.tsx',
          type: 'component',
          content: this.generateLayout()
        }
      );
    }

    // Add styles
    files.push({
      name: 'app/globals.css',
      path: 'app/globals.css',
      type: 'style',
      content: this.generateGlobalStyles()
    });

    return files;
  }

  private async generateFileWithStreaming(file: ProjectFile): Promise<void> {
    this.emit('file_start', {
      type: 'file_start',
      fileName: file.name,
      timestamp: Date.now()
    });

    const content = file.content;
    const chunks = this.splitIntoChunks(content);
    let accumulatedContent = '';

    for (let i = 0; i < chunks.length; i++) {
      accumulatedContent += chunks[i];
      
      this.emit('file_progress', {
        type: 'file_progress',
        fileName: file.name,
        partialContent: accumulatedContent,
        progress: ((i + 1) / chunks.length) * 100,
        timestamp: Date.now()
      });

      // Variable delay based on chunk type
      const delay = this.getChunkDelay(chunks[i]);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.currentFiles.push(file);
    
    this.emit('file_complete', {
      type: 'file_complete',
      fileName: file.name,
      content: accumulatedContent,
      timestamp: Date.now()
    });
  }

  private splitIntoChunks(content: string): string[] {
    const lines = content.split('\n');
    const chunks: string[] = [];
    
    for (const line of lines) {
      // Split longer lines into smaller chunks
      if (line.length > 80) {
        const words = line.split(' ');
        let currentChunk = '';
        
        for (const word of words) {
          if (currentChunk.length + word.length > 60) {
            if (currentChunk) chunks.push(currentChunk + '\n');
            currentChunk = word + ' ';
          } else {
            currentChunk += word + ' ';
          }
        }
        
        if (currentChunk.trim()) chunks.push(currentChunk.trim() + '\n');
      } else {
        chunks.push(line + '\n');
      }
    }
    
    return chunks;
  }

  private getChunkDelay(chunk: string): number {
    // Slower for complex syntax
    if (chunk.includes('function') || chunk.includes('const') || chunk.includes('interface')) {
      return 150;
    }
    if (chunk.includes('{') || chunk.includes('}')) {
      return 100;
    }
    if (chunk.includes('import') || chunk.includes('export')) {
      return 80;
    }
    // Faster for simple content
    return 30;
  }

  private detectProjectType(prompt: string): string {
    const lower = prompt.toLowerCase();
    if (lower.includes('ecommerce') || lower.includes('shop') || lower.includes('cart')) {
      return 'ecommerce';
    }
    if (lower.includes('dashboard') || lower.includes('admin') || lower.includes('analytics')) {
      return 'dashboard';
    }
    if (lower.includes('blog') || lower.includes('content')) {
      return 'blog';
    }
    return 'generic';
  }

  private generatePackageJson(projectType: string): string {
    return `{
  "name": "ai-generated-${projectType}",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "next": "14.0.0",
    "typescript": "^5.0.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.31",
    "lucide-react": "^0.294.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "eslint": "^8.54.0",
    "eslint-config-next": "14.0.0"
  }
}`;
  }

  private generateTailwindConfig(): string {
    return `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
      },
    },
  },
  plugins: [],
}`;
  }

  private generateNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['images.unsplash.com', 'plus.unsplash.com'],
  },
}

module.exports = nextConfig`;
  }

  private generateEcommercePage(): string {
    return `"use client";

import { useState } from 'react';
import Image from 'next/image';
import { ShoppingCart, Star, Heart } from 'lucide-react';
import { ProductCard } from '@/components/ProductCard';
import { Header } from '@/components/Header';

const FEATURED_PRODUCTS = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    price: 199.99,
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
    description: 'High-quality wireless headphones with noise cancellation'
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    price: 299.99,
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400',
    description: 'Advanced fitness tracking with heart rate monitoring'
  },
  {
    id: 3,
    name: 'Portable Bluetooth Speaker',
    price: 89.99,
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400',
    description: 'Compact speaker with powerful sound and long battery life'
  },
];

export default function EcommercePage() {
  const [cartItems, setCartItems] = useState(0);

  const addToCart = () => {
    setCartItems(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header cartItems={cartItems} />
      
      {/* Hero Section */}
      <section className="relative h-96 bg-gradient-to-r from-blue-600 to-purple-700">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative container mx-auto px-4 h-full flex items-center">
          <div className="text-white max-w-2xl">
            <h1 className="text-5xl font-bold mb-4">
              Discover Amazing Products
            </h1>
            <p className="text-xl mb-6">
              Shop the latest tech gadgets and accessories with fast shipping
            </p>
            <button className="bg-white text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Shop Now
            </button>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12">Featured Products</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {FEATURED_PRODUCTS.map(product => (
            <ProductCard 
              key={product.id} 
              product={product}
              onAddToCart={addToCart}
            />
          ))}
        </div>
      </section>
    </div>
  );
}`;
  }

  private generateProductCard(): string {
    return `"use client";

import Image from 'next/image';
import { Star, Heart, ShoppingCart } from 'lucide-react';
import { useState } from 'react';

interface Product {
  id: number;
  name: string;
  price: number;
  rating: number;
  image: string;
  description: string;
}

interface ProductCardProps {
  product: Product;
  onAddToCart: () => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const [isLiked, setIsLiked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className="bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        <Image
          src={product.image}
          alt={product.name}
          width={400}
          height={300}
          className="w-full h-48 object-cover"
        />
        <button
          onClick={() => setIsLiked(!isLiked)}
          className={\`absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white transition-colors \${isLiked ? 'text-red-500' : 'text-gray-600'}\`}
        >
          <Heart className={\`w-5 h-5 \${isLiked ? 'fill-current' : ''}\`} />
        </button>
      </div>
      
      <div className="p-6">
        <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-3">{product.description}</p>
        
        <div className="flex items-center mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={\`w-4 h-4 \${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}\`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600 ml-2">({product.rating})</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-2xl font-bold text-gray-900">
            \${product.price}
          </span>
          <button
            onClick={onAddToCart}
            className={\`flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors \${isHovered ? 'scale-105' : ''}\`}
          >
            <ShoppingCart className="w-4 h-4" />
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
};`;
  }

  private generateShoppingCart(): string {
    return `"use client";

import { useState } from 'react';
import { X, Plus, Minus, ShoppingBag } from 'lucide-react';

interface CartItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

interface ShoppingCartProps {
  isOpen: boolean;
  onClose: () => void;
  items: CartItem[];
}

export const ShoppingCart: React.FC<ShoppingCartProps> = ({ isOpen, onClose, items }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>(items);

  const updateQuantity = (id: number, change: number) => {
    setCartItems(prev => 
      prev.map(item => 
        item.id === id 
          ? { ...item, quantity: Math.max(0, item.quantity + change) }
          : item
      ).filter(item => item.quantity > 0)
    );
  };

  const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <ShoppingBag className="w-5 h-5" />
            Shopping Cart
          </h2>
          <button onClick={onClose} className="p-1 hover:bg-gray-100 rounded">
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4">
          {cartItems.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Your cart is empty</p>
            </div>
          ) : (
            <div className="space-y-4">
              {cartItems.map(item => (
                <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium text-sm">{item.name}</h3>
                    <p className="text-gray-600">\${item.price}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <button 
                        onClick={() => updateQuantity(item.id, -1)}
                        className="w-6 h-6 flex items-center justify-center border rounded"
                      >
                        <Minus className="w-3 h-3" />
                      </button>
                      <span className="w-8 text-center">{item.quantity}</span>
                      <button 
                        onClick={() => updateQuantity(item.id, 1)}
                        className="w-6 h-6 flex items-center justify-center border rounded"
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {cartItems.length > 0 && (
          <div className="border-t p-4">
            <div className="flex justify-between items-center mb-4">
              <span className="text-lg font-semibold">Total: \${total.toFixed(2)}</span>
            </div>
            <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
              Checkout
            </button>
          </div>
        )}
      </div>
    </div>
  );
};`;
  }

  private generateHeader(): string {
    return `"use client";

import { ShoppingCart, Search, User, Menu } from 'lucide-react';
import { useState } from 'react';

interface HeaderProps {
  cartItems: number;
}

export const Header: React.FC<HeaderProps> = ({ cartItems }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center gap-8">
            <h1 className="text-2xl font-bold text-gray-900">TechStore</h1>
            
            {/* Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              <a href="#" className="text-gray-600 hover:text-gray-900">Home</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Products</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Categories</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">About</a>
            </nav>
          </div>
          
          {/* Search Bar */}
          <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex items-center gap-4">
            <button className="p-2 hover:bg-gray-100 rounded-lg">
              <User className="w-5 h-5" />
            </button>
            
            <button className="relative p-2 hover:bg-gray-100 rounded-lg">
              <ShoppingCart className="w-5 h-5" />
              {cartItems > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartItems}
                </span>
              )}
            </button>
            
            <button 
              className="md:hidden p-2 hover:bg-gray-100 rounded-lg"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <Menu className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t py-4">
            <nav className="flex flex-col gap-2">
              <a href="#" className="py-2 text-gray-600 hover:text-gray-900">Home</a>
              <a href="#" className="py-2 text-gray-600 hover:text-gray-900">Products</a>
              <a href="#" className="py-2 text-gray-600 hover:text-gray-900">Categories</a>
              <a href="#" className="py-2 text-gray-600 hover:text-gray-900">About</a>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};`;
  }

  private generateDashboardPage(): string {
    return `"use client";

import { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { MetricsCard } from '@/components/MetricsCard';
import { TrendingUp, Users, DollarSign, ShoppingBag } from 'lucide-react';

export default function DashboardPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const metrics = [
    { title: 'Total Revenue', value: '$54,239', change: '+12.3%', icon: DollarSign, trend: 'up' },
    { title: 'Active Users', value: '2,847', change: '+8.7%', icon: Users, trend: 'up' },
    { title: 'Orders', value: '1,429', change: '+15.2%', icon: ShoppingBag, trend: 'up' },
    { title: 'Growth Rate', value: '23.1%', change: '+3.1%', icon: TrendingUp, trend: 'up' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 mb-8">Dashboard Overview</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {metrics.map((metric, index) => (
                <MetricsCard key={index} {...metric} />
              ))}
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map(i => (
                    <div key={i} className="flex items-center gap-3 p-3 bg-gray-50 rounded">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">New user registered</p>
                        <p className="text-sm text-gray-600">{i * 5} minutes ago</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow">
                <h2 className="text-lg font-semibold mb-4">Top Products</h2>
                <div className="space-y-4">
                  {['Wireless Headphones', 'Smart Watch', 'Bluetooth Speaker', 'Gaming Mouse'].map((product, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <span className="font-medium">{product}</span>
                      <span className="text-green-600 font-semibold">${(299 - i * 50).toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}`;
  }

  private generateSidebar(): string {
    return `"use client";

import { Home, BarChart3, Users, Settings, ShoppingBag, X } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const menuItems = [
    { icon: Home, label: 'Dashboard', active: true },
    { icon: BarChart3, label: 'Analytics', active: false },
    { icon: Users, label: 'Users', active: false },
    { icon: ShoppingBag, label: 'Orders', active: false },
    { icon: Settings, label: 'Settings', active: false },
  ];

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-40 lg:hidden" onClick={onClose}>
          <div className="absolute inset-0 bg-gray-600 opacity-75" />
        </div>
      )}
      
      {/* Sidebar */}
      <div className={\`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform \${isOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\`}>
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
          <button 
            onClick={onClose}
            className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
        
        <nav className="mt-6">
          <div className="px-3">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <a
                  key={index}
                  href="#"
                  className={\`flex items-center px-3 py-2 mt-2 text-sm font-medium rounded-md transition-colors \${
                    item.active
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }\`}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.label}
                </a>
              );
            })}
          </div>
        </nav>
      </div>
    </>
  );
};`;
  }

  private generateMetricsCard(): string {
    return `"use client";

import { LucideIcon } from 'lucide-react';

interface MetricsCardProps {
  title: string;
  value: string;
  change: string;
  icon: LucideIcon;
  trend: 'up' | 'down';
}

export const MetricsCard: React.FC<MetricsCardProps> = ({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  trend 
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
          <Icon className="w-6 h-6 text-blue-600" />
        </div>
      </div>
      
      <div className="mt-4">
        <span className={\`text-sm font-medium \${trend === 'up' ? 'text-green-600' : 'text-red-600'}\`}>
          {change}
        </span>
        <span className="text-sm text-gray-600 ml-1">from last month</span>
      </div>
    </div>
  );
};`;
  }

  private generateGenericPage(): string {
    return `"use client";

import { Layout } from '@/components/Layout';
import Image from 'next/image';

export default function HomePage() {
  return (
    <Layout>
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative h-screen flex items-center justify-center bg-gradient-to-r from-blue-600 to-purple-700">
          <div className="text-center text-white">
            <h1 className="text-6xl font-bold mb-6">Welcome to Our Platform</h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Experience the future of digital innovation with our cutting-edge solutions
            </p>
            <button className="bg-white text-gray-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors">
              Get Started
            </button>
          </div>
        </section>
        
        {/* Features Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold text-center mb-16">Our Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map(i => (
                <div key={i} className="text-center p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">{i}</span>
                  </div>
                  <h3 className="text-xl font-semibold mb-4">Feature {i}</h3>
                  <p className="text-gray-600">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                    Sed do eiusmod tempor incididunt ut labore.
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}`;
  }

  private generateLayout(): string {
    return `"use client";

import { ReactNode } from 'react';

interface LayoutProps {
  children: ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-bold">Your App</h1>
            <div className="flex items-center gap-6">
              <a href="#" className="text-gray-600 hover:text-gray-900">Home</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">About</a>
              <a href="#" className="text-gray-600 hover:text-gray-900">Contact</a>
            </div>
          </div>
        </div>
      </nav>
      
      <main>{children}</main>
      
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Your App. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};`;
  }

  private generateGlobalStyles(): string {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
}`;
  }

  stop() {
    this.isGenerating = false;
  }
}

export const realTimeGenerator = new RealTimeGenerator();