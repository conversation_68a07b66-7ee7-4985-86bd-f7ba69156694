"use client";

import { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Clock, Play, Pause, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";

export interface GenerationStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  type: 'setup' | 'dependency' | 'file' | 'finalize';
  fileName?: string;
  progress?: number;
  startTime?: number;
  endTime?: number;
  error?: string;
}

interface ProjectData {
  files: { [path: string]: string };
  projectType: string;
  title: string;
}

interface UseGenerationProgressReturn {
  steps: GenerationStep[];
  currentStep: GenerationStep | null;
  progress: number;
  isComplete: boolean;
  isRunning: boolean;
  startGeneration: () => void;
  pauseGeneration: () => void;
  resetGeneration: () => void;
}

export function useGenerationProgress(projectData: ProjectData): UseGenerationProgressReturn {
  const [steps, setSteps] = useState<GenerationStep[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);

  // Initialize steps based on project data
  useEffect(() => {
    const fileKeys = Object.keys(projectData.files || {});
    const initialSteps: GenerationStep[] = [
      {
        id: 'setup',
        name: 'Project Setup',
        description: 'Initializing project structure',
        status: 'pending',
        type: 'setup'
      },
      {
        id: 'dependencies',
        name: 'Install Dependencies',
        description: 'Installing required packages',
        status: 'pending',
        type: 'dependency'
      }
    ];

    // Add file generation steps
    fileKeys.forEach((fileName, index) => {
      initialSteps.push({
        id: `file-${index}`,
        name: `Generate ${fileName}`,
        description: `Creating ${fileName}`,
        status: 'pending',
        type: 'file',
        fileName
      });
    });

    // Add finalization step
    initialSteps.push({
      id: 'finalize',
      name: 'Finalize Project',
      description: 'Completing project setup',
      status: 'pending',
      type: 'finalize'
    });

    setSteps(initialSteps);
    setCurrentStepIndex(0);
    setProgress(0);
  }, [projectData]);

  // Calculate progress
  useEffect(() => {
    if (steps.length === 0) return;
    
    const completedSteps = steps.filter(step => step.status === 'completed').length;
    const inProgressSteps = steps.filter(step => step.status === 'in-progress').length;
    
    // Add partial progress for in-progress steps
    let totalProgress = completedSteps;
    if (inProgressSteps > 0) {
      const currentStep = steps.find(step => step.status === 'in-progress');
      if (currentStep?.progress) {
        totalProgress += currentStep.progress / 100;
      } else {
        totalProgress += 0.5; // Default 50% for in-progress
      }
    }
    
    const progressPercentage = (totalProgress / steps.length) * 100;
    setProgress(Math.min(progressPercentage, 100));
  }, [steps]);

  const updateStepStatus = useCallback((stepId: string, status: GenerationStep['status'], progress?: number, error?: string) => {
    setSteps(prev => prev.map(step => {
      if (step.id === stepId) {
        const updatedStep = { 
          ...step, 
          status,
          progress,
          error
        };
        
        if (status === 'in-progress' && !step.startTime) {
          updatedStep.startTime = Date.now();
        }
        
        if (status === 'completed' || status === 'error') {
          updatedStep.endTime = Date.now();
          updatedStep.progress = status === 'completed' ? 100 : 0;
        }
        
        return updatedStep;
      }
      return step;
    }));
  }, []);

  const processNextStep = useCallback(async () => {
    if (!isRunning || currentStepIndex >= steps.length) return;
    
    const currentStep = steps[currentStepIndex];
    
    if (currentStep.status === 'pending') {
      // Start current step
      updateStepStatus(currentStep.id, 'in-progress');
      
      // Simulate step processing
      const simulateProgress = async () => {
        const duration = currentStep.type === 'file' ? 2000 : 1500;
        const intervals = 20;
        const progressIncrement = 100 / intervals;
        
        for (let i = 0; i <= intervals; i++) {
          if (!isRunning) break;
          
          const stepProgress = Math.min(i * progressIncrement, 100);
          updateStepStatus(currentStep.id, 'in-progress', stepProgress);
          
          await new Promise(resolve => setTimeout(resolve, duration / intervals));
        }
        
        if (isRunning) {
          updateStepStatus(currentStep.id, 'completed', 100);
          setCurrentStepIndex(prev => prev + 1);
        }
      };
      
      await simulateProgress();
    }
  }, [currentStepIndex, isRunning, steps, updateStepStatus]);

  // Process steps when running
  useEffect(() => {
    if (isRunning && currentStepIndex < steps.length) {
      processNextStep();
    } else if (currentStepIndex >= steps.length) {
      setIsRunning(false);
    }
  }, [isRunning, currentStepIndex, processNextStep, steps.length]);

  const startGeneration = useCallback(() => {
    setIsRunning(true);
  }, []);

  const pauseGeneration = useCallback(() => {
    setIsRunning(false);
  }, []);

  const resetGeneration = useCallback(() => {
    setIsRunning(false);
    setCurrentStepIndex(0);
    setProgress(0);
    setSteps(prev => prev.map(step => ({
      ...step,
      status: 'pending' as const,
      progress: 0,
      startTime: undefined,
      endTime: undefined,
      error: undefined
    })));
  }, []);

  const currentStep = steps[currentStepIndex] || null;
  const isComplete = steps.length > 0 && steps.every(step => step.status === 'completed');

  return {
    steps,
    currentStep,
    progress,
    isComplete,
    isRunning,
    startGeneration,
    pauseGeneration,
    resetGeneration
  };
}

interface GenerationProgressProps {
  steps: GenerationStep[];
  currentStep: GenerationStep | null;
  onStepClick?: (step: GenerationStep) => void;
  className?: string;
}

export function GenerationProgress({ 
  steps, 
  currentStep, 
  onStepClick,
  className 
}: GenerationProgressProps) {
  const getStepIcon = (step: GenerationStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in-progress':
        return <Play className="w-4 h-4 text-blue-500 animate-pulse" />;
      case 'error':
        return <RotateCcw className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStepBadge = (step: GenerationStep) => {
    switch (step.type) {
      case 'setup':
        return <Badge variant="outline" className="text-xs">Setup</Badge>;
      case 'dependency':
        return <Badge variant="outline" className="text-xs">Deps</Badge>;
      case 'file':
        return <Badge variant="outline" className="text-xs">File</Badge>;
      case 'finalize':
        return <Badge variant="outline" className="text-xs">Final</Badge>;
      default:
        return null;
    }
  };

  const formatDuration = (startTime?: number, endTime?: number) => {
    if (!startTime) return '';
    const duration = (endTime || Date.now()) - startTime;
    return `${(duration / 1000).toFixed(1)}s`;
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="space-y-2">
        {steps.map((step) => (
          <Card 
            key={step.id}
            className={cn(
              "p-3 cursor-pointer transition-all duration-200",
              "hover:shadow-md hover:scale-[1.02]",
              step.status === 'in-progress' && "border-blue-500 bg-blue-50/50",
              step.status === 'completed' && "border-green-500 bg-green-50/50",
              step.status === 'error' && "border-red-500 bg-red-50/50",
              currentStep?.id === step.id && "ring-2 ring-blue-500/50"
            )}
            onClick={() => onStepClick?.(step)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                {getStepIcon(step)}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm truncate">
                      {step.name}
                    </span>
                    {getStepBadge(step)}
                  </div>
                  
                  <p className="text-xs text-muted-foreground mt-1">
                    {step.description}
                  </p>
                  
                  {step.fileName && (
                    <p className="text-xs text-blue-600 mt-1 font-mono">
                      {step.fileName}
                    </p>
                  )}
                  
                  {step.error && (
                    <p className="text-xs text-red-600 mt-1">
                      Error: {step.error}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex flex-col items-end gap-1">
                {step.status === 'in-progress' && step.progress !== undefined && (
                  <div className="w-16">
                    <Progress value={step.progress} className="h-1" />
                  </div>
                )}
                
                {(step.startTime || step.endTime) && (
                  <span className="text-xs text-muted-foreground">
                    {formatDuration(step.startTime, step.endTime)}
                  </span>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      {steps.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Generation steps will appear here</p>
        </div>
      )}
    </div>
  );
}