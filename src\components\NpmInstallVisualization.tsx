import React, { useState, useEffect } from 'react';
import { Package, Download, CheckCircle, Loader2, FolderOpen, Clock } from 'lucide-react';

export interface PackageInfo {
  name: string;
  version: string;
  description?: string;
  size?: string;
  dependencies?: string[];
  status: 'pending' | 'downloading' | 'installing' | 'complete';
  progress?: number;
}

interface NpmInstallVisualizationProps {
  packages: PackageInfo[];
  onInstallComplete?: () => void;
  showTerminal?: boolean;
  className?: string;
}

export const NpmInstallVisualization: React.FC<NpmInstallVisualizationProps> = ({
  packages,
  onInstallComplete,
  showTerminal = true,
  className = ""
}) => {
  const [currentPackageIndex, setCurrentPackageIndex] = useState(0);
  const [terminalOutput, setTerminalOutput] = useState<string[]>([]);
  const [isInstalling, setIsInstalling] = useState(false);
  const [startTime, setStartTime] = useState<Date | null>(null);

  useEffect(() => {
    if (packages.length > 0 && !isInstalling) {
      startInstallation();
    }
  }, [packages]);

  const startInstallation = async () => {
    setIsInstalling(true);
    setStartTime(new Date());
    setTerminalOutput(['$ npm install', '']);

    for (let i = 0; i < packages.length; i++) {
      setCurrentPackageIndex(i);
      await installPackage(packages[i], i);
    }

    // Generate package-lock.json
    setTerminalOutput(prev => [
      ...prev,
      '',
      'added ' + packages.length + ' packages, and audited ' + (packages.length + Math.floor(Math.random() * 50)) + ' packages in ' + Math.floor(Math.random() * 10 + 5) + 's',
      '',
      packages.length + ' packages are looking for funding',
      '  run `npm fund` for details',
      '',
      'found 0 vulnerabilities'
    ]);

    setIsInstalling(false);
    onInstallComplete?.();
  };

  const installPackage = (pkg: PackageInfo, index: number): Promise<void> => {
    return new Promise((resolve) => {
      // Add download message
      setTerminalOutput(prev => [
        ...prev,
        `⠋ Installing ${pkg.name}@${pkg.version}...`
      ]);

      // Simulate download progress
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          clearInterval(progressInterval);
          
          // Update terminal with completion
          setTerminalOutput(prev => [
            ...prev.slice(0, -1), // Remove the installing message
            `✓ ${pkg.name}@${pkg.version}`,
            ...(pkg.dependencies ? pkg.dependencies.map(dep => `  └── ${dep}`) : [])
          ]);

          // Mark package as complete
          packages[index].status = 'complete';
          
          setTimeout(resolve, 200 + Math.random() * 300);
        }
      }, 100);
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'downloading':
        return <Download className="w-4 h-4 text-blue-500 animate-bounce" />;
      case 'installing':
        return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const completedPackages = packages.filter(pkg => pkg.status === 'complete').length;
  const totalSize = packages.reduce((sum, pkg) => {
    const size = pkg.size ? parseFloat(pkg.size.replace(/[^\d.]/g, '')) : 0;
    return sum + size;
  }, 0);

  const elapsedTime = startTime ? Math.floor((Date.now() - startTime.getTime()) / 1000) : 0;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Package className="w-6 h-6 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Installing Dependencies
              </h3>
              <p className="text-sm text-gray-600">
                {completedPackages} of {packages.length} packages installed
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-red-600">
              {packages.length}
            </div>
            <div className="text-xs text-gray-500">packages</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-red-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(completedPackages / packages.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {totalSize.toFixed(1)}MB
            </div>
            <div className="text-xs text-gray-500">Total Size</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {elapsedTime}s
            </div>
            <div className="text-xs text-gray-500">Elapsed</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {isInstalling ? 'Installing...' : 'Complete'}
            </div>
            <div className="text-xs text-gray-500">Status</div>
          </div>
        </div>
      </div>

      {/* Package List */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="px-6 py-4 border-b bg-gray-50 rounded-t-lg">
          <h4 className="text-sm font-medium text-gray-900">Package Installation</h4>
        </div>
        <div className="p-4 max-h-64 overflow-y-auto">
          <div className="space-y-2">
            {packages.map((pkg, index) => (
              <div
                key={pkg.name}
                className={`
                  flex items-center gap-3 p-3 rounded-lg transition-all duration-300
                  ${pkg.status === 'installing' ? 'bg-yellow-50 border border-yellow-200' : ''}
                  ${pkg.status === 'complete' ? 'bg-green-50' : ''}
                  ${index === currentPackageIndex && isInstalling ? 'ring-2 ring-blue-500' : ''}
                `}
              >
                <div className="flex-shrink-0">
                  {getStatusIcon(pkg.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {pkg.name}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {pkg.version}
                    </span>
                    {pkg.size && (
                      <span className="text-xs text-gray-400">
                        {pkg.size}
                      </span>
                    )}
                  </div>
                  {pkg.description && (
                    <p className="text-xs text-gray-600 truncate mt-1">
                      {pkg.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Terminal Output */}
      {showTerminal && (
        <div className="bg-gray-900 rounded-lg border shadow-sm">
          <div className="flex items-center gap-2 px-4 py-2 bg-gray-800 rounded-t-lg">
            <div className="flex gap-1">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <span className="text-sm text-gray-300 ml-2">Terminal</span>
          </div>
          <div className="p-4 h-48 overflow-y-auto">
            <div className="font-mono text-sm text-green-400 space-y-1">
              {terminalOutput.map((line, index) => (
                <div key={index} className="whitespace-pre-wrap">
                  {line}
                  {index === terminalOutput.length - 1 && isInstalling && (
                    <span className="animate-pulse">|</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Generated Files */}
      {!isInstalling && completedPackages === packages.length && (
        <div className="bg-white rounded-lg border shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <FolderOpen className="w-5 h-5 text-blue-500" />
            <h4 className="text-sm font-medium text-gray-900">Generated Files</h4>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>package-lock.json</span>
              <span className="text-xs text-gray-400">
                ({Math.floor(packages.length * 2.5)}KB)
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>node_modules/</span>
              <span className="text-xs text-gray-400">
                ({packages.length} packages)
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Predefined package lists for common project types
export const createPackageList = (projectType: string = 'react'): PackageInfo[] => {
  const basePackages: PackageInfo[] = [
    {
      name: 'react',
      version: '18.2.0',
      description: 'A JavaScript library for building user interfaces',
      size: '2.5MB',
      status: 'pending'
    },
    {
      name: 'react-dom',
      version: '18.2.0',
      description: 'React package for working with the DOM',
      size: '1.8MB',
      status: 'pending'
    },
    {
      name: '@types/react',
      version: '18.2.15',
      description: 'TypeScript definitions for React',
      size: '0.3MB',
      status: 'pending'
    },
    {
      name: '@types/react-dom',
      version: '18.2.7',
      description: 'TypeScript definitions for React DOM',
      size: '0.2MB',
      status: 'pending'
    },
    {
      name: 'typescript',
      version: '5.0.2',
      description: 'TypeScript is a language for application scale JavaScript',
      size: '12.1MB',
      status: 'pending'
    },
    {
      name: 'vite',
      version: '4.4.5',
      description: 'Native-ESM powered web dev build tool',
      size: '8.7MB',
      status: 'pending'
    },
    {
      name: '@vitejs/plugin-react',
      version: '4.0.3',
      description: 'The all-in-one Vite plugin for React projects',
      size: '0.5MB',
      status: 'pending'
    },
    {
      name: 'tailwindcss',
      version: '3.3.3',
      description: 'A utility-first CSS framework',
      size: '4.2MB',
      status: 'pending'
    },
    {
      name: 'autoprefixer',
      version: '10.4.14',
      description: 'Parse CSS and add vendor prefixes automatically',
      size: '1.1MB',
      status: 'pending'
    },
    {
      name: 'postcss',
      version: '8.4.27',
      description: 'Tool for transforming CSS with JavaScript',
      size: '0.8MB',
      status: 'pending'
    },
    {
      name: 'lucide-react',
      version: '0.263.1',
      description: 'Beautiful & consistent icon toolkit',
      size: '1.2MB',
      status: 'pending'
    },
    {
      name: 'eslint',
      version: '8.45.0',
      description: 'An AST-based pattern checker for JavaScript',
      size: '3.4MB',
      status: 'pending'
    }
  ];

  return basePackages;
};
