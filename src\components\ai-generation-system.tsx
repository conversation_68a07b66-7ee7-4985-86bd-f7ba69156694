"use client";

import React, { useState, useCallback } from 'react';
import { BoltGenerationSuite } from './bolt-generation-suite';
import { EnhancedLoadingScreen, type LoadingStep } from './enhanced-loading-screen';
import { webSocketService } from '@/lib/websocket-service';
import { unsplashService } from '@/lib/unsplash';
import { 
  FileText, 
  Folder, 
  Package, 
  Settings, 
  Image, 
  Code,
  Zap
} from 'lucide-react';

interface AIGenerationSystemProps {
  projectPrompt: string;
  onComplete?: (files: Record<string, string>) => void;
  className?: string;
}

// Create comprehensive loading steps based on project requirements
const createLoadingSteps = (projectPrompt: string): LoadingStep[] => {
  const baseSteps: LoadingStep[] = [
    {
      id: 'init',
      title: 'Initializing Project',
      description: 'Setting up AI generation environment and project structure',
      status: 'waiting',
      icon: Zap,
      estimatedTime: 1000,
    },
    {
      id: 'analysis',
      title: 'Analyzing Requirements',
      description: 'AI is understanding your project requirements and scope',
      status: 'waiting', 
      icon: Code,
      estimatedTime: 2000,
    },
    {
      id: 'images',
      title: 'Fetching Contextual Images',
      description: 'Getting high-quality, relevant images from Unsplash',
      status: 'waiting',
      icon: Image,
      estimatedTime: 3000,
    },
    {
      id: 'structure',
      title: 'Creating Project Structure',
      description: 'Building folder hierarchy and file organization',
      status: 'waiting',
      icon: Folder,
      estimatedTime: 1500,
    },
    {
      id: 'packages',
      title: 'Installing Dependencies',
      description: 'Adding required packages and configuration files',
      status: 'waiting',
      icon: Package,
      estimatedTime: 4000,
    },
    {
      id: 'config',
      title: 'Generating Configuration',
      description: 'Creating configuration files and build settings',
      status: 'waiting',
      icon: Settings,
      estimatedTime: 2000,
    },
  ];

  // Add dynamic file generation steps based on project complexity
  const componentSteps: LoadingStep[] = [];
  const projectLower = projectPrompt.toLowerCase();
  
  // Determine project complexity and add appropriate component steps
  if (projectLower.includes('ecommerce') || projectLower.includes('shop')) {
    componentSteps.push(
      {
        id: 'product-list',
        title: 'Product Listing Component',
        description: 'Creating product display and catalog functionality',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 3000,
      },
      {
        id: 'shopping-cart',
        title: 'Shopping Cart System',
        description: 'Building cart functionality and checkout flow',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 4000,
      }
    );
  }
  
  if (projectLower.includes('dashboard') || projectLower.includes('admin')) {
    componentSteps.push(
      {
        id: 'dashboard',
        title: 'Dashboard Layout',
        description: 'Creating admin interface and data visualization',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 3500,
      },
      {
        id: 'charts',
        title: 'Analytics Components',
        description: 'Building charts and metrics visualization',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 3000,
      }
    );
  }
  
  if (projectLower.includes('blog') || projectLower.includes('content')) {
    componentSteps.push(
      {
        id: 'blog-layout',
        title: 'Blog Layout System',
        description: 'Creating article display and navigation',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 2500,
      },
      {
        id: 'article-components',
        title: 'Article Components',
        description: 'Building content display and reading experience',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 3000,
      }
    );
  }

  // Add generic component steps if no specific type detected
  if (componentSteps.length === 0) {
    componentSteps.push(
      {
        id: 'main-layout',
        title: 'Main Layout Component',
        description: 'Creating primary application structure and navigation',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 3000,
      },
      {
        id: 'feature-components',
        title: 'Feature Components',
        description: 'Building core functionality and user interface',
        status: 'waiting',
        icon: FileText,
        estimatedTime: 4000,
      }
    );
  }

  // Always add final steps
  const finalSteps: LoadingStep[] = [
    {
      id: 'styling',
      title: 'Applying Styles',
      description: 'Implementing responsive design and visual polish',
      status: 'waiting',
      icon: FileText,
      estimatedTime: 2000,
    },
    {
      id: 'integration',
      title: 'Final Integration',
      description: 'Connecting all components and ensuring functionality',
      status: 'waiting',
      icon: Code,
      estimatedTime: 2500,
    },
  ];

  return [...baseSteps, ...componentSteps, ...finalSteps];
};

export const AIGenerationSystem: React.FC<AIGenerationSystemProps> = ({
  projectPrompt,
  onComplete,
  className
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showLoadingScreen, setShowLoadingScreen] = useState(false);
  const [loadingSteps, setLoadingSteps] = useState<LoadingStep[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);

  const handleStartGeneration = useCallback(() => {
    // Skip loading screen entirely - go directly to real-time generation
    setIsGenerating(true);
    setShowLoadingScreen(false);
  }, [projectPrompt]);

  const handleLoadingComplete = useCallback(() => {
    setShowLoadingScreen(false);
  }, []);

  const handleGenerationComplete = useCallback((files: Record<string, string>) => {
    setIsGenerating(false);
    onComplete?.(files);
  }, [onComplete]);

  // Always show the main generation interface for real-time experience
  return (
    <BoltGenerationSuite
      projectPrompt={projectPrompt}
      onGenerationComplete={handleGenerationComplete}
      onStartGeneration={handleStartGeneration}
      className={className}
    />
  );
};