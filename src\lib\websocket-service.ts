"use client";

import { EventEmitter } from 'events';

export interface GenerationEvent {
  type: 'step_start' | 'step_progress' | 'step_complete' | 'file_created' | 'file_updated' | 'generation_complete' | 'error';
  stepId?: string;
  fileName?: string;
  content?: string;
  progress?: number;
  error?: string;
  timestamp: number;
}

export interface WebSocketMessage {
  type: 'generation_event';
  data: GenerationEvent;
}

export class WebSocketService extends EventEmitter {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private messageQueue: WebSocketMessage[] = [];

  constructor() {
    super();
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        this.once('connected', resolve);
        this.once('error', reject);
        return;
      }

      this.isConnecting = true;

      try {
        // In a real implementation, this would connect to your WebSocket server
        // For now, we'll simulate the connection
        this.simulateConnection();
        
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.emit('connected');
        this.processMessageQueue();
        resolve();
      } catch (error) {
        this.isConnecting = false;
        this.emit('error', error);
        reject(error);
      }
    });
  }

  private simulateConnection() {
    // Simulate WebSocket connection for demo purposes
    // In production, replace with actual WebSocket connection
    this.ws = {
      readyState: WebSocket.OPEN,
      send: (data: string) => {
        // Echo back for simulation
        setTimeout(() => {
          this.handleMessage({ data });
        }, 100);
      },
      close: () => {
        this.ws = null;
        this.emit('disconnected');
      }
    } as WebSocket;
  }

  private handleMessage(event: { data: string }) {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      if (message.type === 'generation_event') {
        this.emit('generation_event', message.data);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      if (message) {
        this.send(message);
      }
    }
  }

  private async reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('error', new Error('Max reconnection attempts reached'));
      return;
    }

    this.reconnectAttempts++;
    
    await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
    
    try {
      await this.connect();
    } catch (error) {
      this.reconnect();
    }
  }

  send(message: WebSocketMessage) {
    if (!this.isConnected()) {
      this.messageQueue.push(message);
      return;
    }

    try {
      this.ws?.send(JSON.stringify(message));
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      this.messageQueue.push(message);
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.removeAllListeners();
  }

  // Generation-specific methods
  startGeneration(projectPrompt: string) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'step_start',
        stepId: 'generation_start',
        content: projectPrompt,
        timestamp: Date.now()
      }
    });
  }

  updateProgress(stepId: string, progress: number) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'step_progress',
        stepId,
        progress,
        timestamp: Date.now()
      }
    });
  }

  completeStep(stepId: string, content?: string) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'step_complete',
        stepId,
        content,
        timestamp: Date.now()
      }
    });
  }

  createFile(fileName: string, content: string) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'file_created',
        fileName,
        content,
        timestamp: Date.now()
      }
    });
  }

  updateFile(fileName: string, content: string) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'file_updated',
        fileName,
        content,
        timestamp: Date.now()
      }
    });
  }

  completeGeneration() {
    this.send({
      type: 'generation_event',
      data: {
        type: 'generation_complete',
        timestamp: Date.now()
      }
    });
  }

  reportError(error: string, stepId?: string) {
    this.send({
      type: 'generation_event',
      data: {
        type: 'error',
        stepId,
        error,
        timestamp: Date.now()
      }
    });
  }
}

// Singleton instance
export const webSocketService = new WebSocketService();