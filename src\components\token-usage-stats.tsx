'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { trpc } from '@/trpc/client';
import { 
  BarChart3, 
  DollarSign, 
  Zap, 
  TrendingUp, 
  RefreshCw,
  Activity,
  Brain,
  MessageSquare
} from 'lucide-react';

interface TokenUsageStatsProps {
  projectId?: string;
  messageId?: string;
  showUserStats?: boolean;
}

export function TokenUsageStats({ projectId, messageId, showUserStats = false }: TokenUsageStatsProps) {
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch user stats if requested
  const { data: userStats, isLoading: userStatsLoading, refetch: refetchUserStats } = trpc.tokenUsage.getUserStats.useQuery(
    undefined,
    { enabled: showUserStats }
  );

  // Fetch project usage if projectId provided
  const { data: projectUsage, isLoading: projectLoading, refetch: refetchProject } = trpc.tokenUsage.getProjectUsage.useQuery(
    { projectId: projectId! },
    { enabled: !!projectId }
  );

  // Fetch message usage if messageId provided
  const { data: messageUsage, isLoading: messageLoading, refetch: refetchMessage } = trpc.tokenUsage.getMessageUsage.useQuery(
    { messageId: messageId! },
    { enabled: !!messageId }
  );

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    if (showUserStats) refetchUserStats();
    if (projectId) refetchProject();
    if (messageId) refetchMessage();
  };

  const formatNumber = (num: number) => num.toLocaleString();
  const formatCost = (cost: number) => `$${cost.toFixed(4)}`;

  if (userStatsLoading || projectLoading || messageLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Token Usage Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Activity className="h-6 w-6" />
          Token Usage Statistics
        </h2>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Message-specific usage */}
      {messageId && messageUsage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Message Token Usage
            </CardTitle>
            <CardDescription>Token usage for this specific message</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {messageUsage.map((usage, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">{usage.agentName}</CardTitle>
                    <Badge variant="secondary">{usage.modelName}</Badge>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Input:</span>
                      <span className="font-mono">{formatNumber(usage.inputTokens)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Output:</span>
                      <span className="font-mono">{formatNumber(usage.outputTokens)}</span>
                    </div>
                    <div className="flex justify-between text-sm font-semibold">
                      <span>Total:</span>
                      <span className="font-mono">{formatNumber(usage.totalTokens)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Cost:</span>
                      <span className="font-mono">{formatCost(usage.estimatedCost || 0)}</span>
                    </div>
                    {usage.requestDuration && (
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>Duration:</span>
                        <span className="font-mono">{usage.requestDuration}ms</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Project usage */}
      {projectId && projectUsage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Project Token Usage
            </CardTitle>
            <CardDescription>Total usage for this project</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatNumber(projectUsage.totalInputTokens)}
                </div>
                <div className="text-sm text-gray-500">Input Tokens</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatNumber(projectUsage.totalOutputTokens)}
                </div>
                <div className="text-sm text-gray-500">Output Tokens</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatNumber(projectUsage.totalTokens)}
                </div>
                <div className="text-sm text-gray-500">Total Tokens</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCost(projectUsage.totalCost)}
                </div>
                <div className="text-sm text-gray-500">Total Cost</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* User stats */}
      {showUserStats && userStats && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="models">By Model</TabsTrigger>
            <TabsTrigger value="agents">By Agent</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(userStats.overall.totalTokens)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCost(userStats.overall.totalCost)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">AI Requests</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(userStats.overall.requestCount)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Cost/Request</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCost(userStats.overall.totalCost / Math.max(userStats.overall.requestCount, 1))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="models" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Usage by Model</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(userStats.byModel).map(([model, stats]: [string, any]) => (
                    <div key={model} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{model}</span>
                        <Badge variant="outline">{stats.requests} requests</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Tokens: </span>
                          <span className="font-mono">{formatNumber(stats.totalTokens)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Cost: </span>
                          <span className="font-mono">{formatCost(stats.cost)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Avg/Request: </span>
                          <span className="font-mono">{formatCost(stats.cost / stats.requests)}</span>
                        </div>
                      </div>
                      <Separator />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="agents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Usage by Agent
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(userStats.byAgent).map(([agent, stats]: [string, any]) => (
                    <div key={agent} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{agent}</span>
                        <Badge variant="outline">{stats.requests} requests</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Tokens: </span>
                          <span className="font-mono">{formatNumber(stats.totalTokens)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Cost: </span>
                          <span className="font-mono">{formatCost(stats.cost)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Avg/Request: </span>
                          <span className="font-mono">{formatCost(stats.cost / stats.requests)}</span>
                        </div>
                      </div>
                      <Separator />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
