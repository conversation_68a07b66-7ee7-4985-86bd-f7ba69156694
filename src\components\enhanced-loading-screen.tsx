"use client";

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Zap, 
  FileText, 
  Folder, 
  Package, 
  Settings, 
  Image, 
  Code, 
  Loader2,
  CheckCircle2,
  ArrowRight,
  <PERSON>rkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface LoadingStep {
  id: string;
  title: string;
  description: string;
  status: 'waiting' | 'active' | 'completed' | 'error';
  icon: React.ComponentType<any>;
  estimatedTime: number;
  actualTime?: number;
}

interface EnhancedLoadingScreenProps {
  steps: LoadingStep[];
  currentStep: number;
  overallProgress: number;
  projectName?: string;
  projectType?: string;
  onComplete?: () => void;
  className?: string;
}

const LoadingAnimation = ({ isActive }: { isActive: boolean }) => (
  <div className="relative w-16 h-16 mx-auto mb-4">
    <div className={cn(
      "absolute inset-0 rounded-full border-4 border-blue-500/20",
      isActive && "animate-pulse"
    )} />
    <div className={cn(
      "absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 animate-spin transition-all duration-500",
      isActive ? "opacity-100" : "opacity-30"
    )} />
    <div className="absolute inset-0 flex items-center justify-center">
      <Zap className={cn(
        "w-6 h-6 transition-colors duration-500",
        isActive ? "text-blue-400" : "text-gray-500"
      )} />
    </div>
  </div>
);

const StepProgress = ({ step, isActive, isCompleted }: { 
  step: LoadingStep; 
  isActive: boolean; 
  isCompleted: boolean;
}) => {
  const Icon = step.icon;
  
  return (
    <div className={cn(
      "flex items-center gap-4 p-4 rounded-lg transition-all duration-300",
      isActive && "bg-blue-500/5 border border-blue-500/20 scale-105",
      isCompleted && "bg-green-500/5 border border-green-500/20",
      !isActive && !isCompleted && "bg-gray-800/30"
    )}>
      <div className={cn(
        "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300",
        isActive && "bg-blue-500/20 text-blue-400",
        isCompleted && "bg-green-500/20 text-green-400",
        !isActive && !isCompleted && "bg-gray-700 text-gray-500"
      )}>
        {isCompleted ? (
          <CheckCircle2 className="w-5 h-5" />
        ) : isActive ? (
          <Loader2 className="w-5 h-5 animate-spin" />
        ) : (
          <Icon className="w-5 h-5" />
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h4 className={cn(
            "font-medium transition-colors duration-300",
            isActive && "text-blue-400",
            isCompleted && "text-green-400",
            !isActive && !isCompleted && "text-gray-400"
          )}>
            {step.title}
          </h4>
          {isActive && (
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 text-xs">
              Processing
            </Badge>
          )}
          {isCompleted && step.actualTime && (
            <Badge variant="outline" className="text-xs border-green-500/50 text-green-400">
              {(step.actualTime / 1000).toFixed(1)}s
            </Badge>
          )}
        </div>
        <p className={cn(
          "text-sm mt-1 transition-colors duration-300",
          isActive && "text-gray-300",
          isCompleted && "text-gray-400",
          !isActive && !isCompleted && "text-gray-500"
        )}>
          {step.description}
        </p>
      </div>
      
      <div className="flex items-center">
        {isActive && (
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
        )}
        {isCompleted && (
          <ArrowRight className="w-4 h-4 text-green-400" />
        )}
      </div>
    </div>
  );
};

const ProjectPreview = ({ projectName, projectType }: { 
  projectName?: string; 
  projectType?: string;
}) => (
  <div className="flex items-center gap-3 p-4 bg-gray-800/50 rounded-lg">
    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
      <Sparkles className="w-6 h-6 text-white" />
    </div>
    <div>
      <h3 className="font-semibold text-white">
        {projectName || 'AI Generated Project'}
      </h3>
      <p className="text-sm text-gray-400">
        {projectType || 'Custom Application'}
      </p>
    </div>
  </div>
);

export const EnhancedLoadingScreen: React.FC<EnhancedLoadingScreenProps> = ({
  steps,
  currentStep,
  overallProgress,
  projectName,
  projectType,
  onComplete,
  className
}) => {
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [startTime] = useState(Date.now());
  
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeElapsed(Date.now() - startTime);
    }, 1000);
    
    return () => clearInterval(timer);
  }, [startTime]);
  
  useEffect(() => {
    if (overallProgress >= 100) {
      setTimeout(() => {
        onComplete?.();
      }, 1000);
    }
  }, [overallProgress, onComplete]);
  
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const isCompleted = overallProgress >= 100;
  const estimatedTimeRemaining = steps
    .slice(currentStep)
    .reduce((total, step) => total + step.estimatedTime, 0);
  
  return (
    <div className={cn(
      "min-h-screen bg-gray-950 flex items-center justify-center p-4",
      className
    )}>
      <Card className="w-full max-w-2xl bg-gray-900 border-gray-800 shadow-2xl">
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <LoadingAnimation isActive={!isCompleted} />
            <h1 className="text-2xl font-bold text-white mb-2">
              {isCompleted ? 'Generation Complete!' : 'Building Your Project'}
            </h1>
            <p className="text-gray-400">
              {isCompleted 
                ? 'Your AI-generated project is ready to use' 
                : 'AI is crafting your custom application...'
              }
            </p>
          </div>
          
          {/* Project Preview */}
          <ProjectPreview projectName={projectName} projectType={projectType} />
          
          {/* Progress Overview */}
          <div className="my-6 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Code className="w-4 h-4 text-blue-400" />
                <span className="text-sm font-medium text-gray-300">
                  Overall Progress
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">
                  {completedSteps}/{totalSteps} steps
                </span>
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                  {Math.round(overallProgress)}%
                </Badge>
              </div>
            </div>
            
            <Progress 
              value={overallProgress} 
              className="h-3 bg-gray-800"
            />
            
            <div className="flex justify-between text-xs text-gray-500">
              <span>Elapsed: {Math.floor(timeElapsed / 1000)}s</span>
              {!isCompleted && (
                <span>Est. remaining: {Math.floor(estimatedTimeRemaining / 1000)}s</span>
              )}
            </div>
          </div>
          
          {/* Steps List */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {steps.map((step, index) => (
              <StepProgress
                key={step.id}
                step={step}
                isActive={index === currentStep}
                isCompleted={step.status === 'completed'}
              />
            ))}
          </div>
          
          {/* Footer Stats */}
          {isCompleted && (
            <div className="mt-6 pt-6 border-t border-gray-800">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-semibold text-green-400">
                    {steps.filter(s => s.icon === FileText).length}
                  </div>
                  <div className="text-xs text-gray-500">Files Created</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-blue-400">
                    {steps.filter(s => s.icon === Package).length}
                  </div>
                  <div className="text-xs text-gray-500">Packages</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-purple-400">
                    {Math.floor(timeElapsed / 1000)}s
                  </div>
                  <div className="text-xs text-gray-500">Total Time</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};