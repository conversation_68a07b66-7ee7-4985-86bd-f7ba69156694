"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { ExternalLinkIcon, CameraIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { UnsplashImage, unsplashService } from '@/lib/unsplash';
import { ImageContext, unsplashPatterns } from '@/lib/unsplash-patterns';

interface UnsplashImageProps {
  // Image source options (use one)
  imageId?: string;
  query?: string;
  context?: ImageContext;
  fallbackSrc?: string;
  
  // Display options
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  size?: 'thumb' | 'small' | 'regular' | 'full';
  
  // Attribution options
  showAttribution?: boolean;
  attributionPosition?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  attributionClassName?: string;
  
  // Behavior options
  trackDownload?: boolean;
  lazy?: boolean;
  priority?: boolean;
  
  // Event handlers
  onLoad?: () => void;
  onError?: () => void;
  onClick?: () => void;
}

/**
 * UnsplashImage component with automatic attribution and smart loading
 */
export function UnsplashImage({
  imageId,
  query,
  context,
  fallbackSrc = '/placeholder-image.jpg',
  alt,
  className,
  width = 800,
  height = 600,
  size = 'regular',
  showAttribution = true,
  attributionPosition = 'bottom-right',
  attributionClassName,
  trackDownload = true,
  lazy = true,
  priority = false,
  onLoad,
  onError,
  onClick,
}: UnsplashImageProps) {
  const [image, setImage] = useState<UnsplashImage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Load image based on props
  useEffect(() => {
    let mounted = true;

    const loadImage = async () => {
      try {
        setLoading(true);
        setError(false);
        
        let fetchedImage: UnsplashImage | null = null;

        // Priority: imageId > context > query
        if (imageId) {
          fetchedImage = await unsplashService.getImageById(imageId);
        } else if (context) {
          fetchedImage = await unsplashPatterns.getContextualImage(context);
        } else if (query) {
          const images = await unsplashService.searchImages(query, { per_page: 1 });
          fetchedImage = images.length > 0 ? images[0] : null;
        }

        if (mounted) {
          if (fetchedImage) {
            setImage(fetchedImage);
          } else {
            setError(true);
          }
          setLoading(false);
        }
      } catch (err) {
        console.error('Error loading Unsplash image:', err);
        if (mounted) {
          setError(true);
          setLoading(false);
        }
      }
    };

    loadImage();

    return () => {
      mounted = false;
    };
  }, [imageId, query, context]);

  // Track download when image is clicked
  const handleImageClick = async () => {
    if (image && trackDownload && image.links.download_location) {
      await unsplashService.trackDownload(image.links.download_location);
    }
    onClick?.();
  };

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
    onLoad?.();
  };

  // Handle image error
  const handleImageError = () => {
    setError(true);
    onError?.();
  };

  // Get image URL based on size
  const getImageUrl = (img: UnsplashImage): string => {
    switch (size) {
      case 'thumb':
        return img.urls.thumb;
      case 'small':
        return img.urls.small;
      case 'regular':
        return img.urls.regular;
      case 'full':
        return img.urls.full;
      default:
        return img.urls.regular;
    }
  };

  // Attribution component
  const Attribution = ({ image }: { image: UnsplashImage }) => {
    const positionClasses = {
      'bottom-left': 'bottom-2 left-2',
      'bottom-right': 'bottom-2 right-2',
      'top-left': 'top-2 left-2',
      'top-right': 'top-2 right-2',
    };

    return (
      <div
        className={cn(
          'absolute z-10 flex items-center gap-1 rounded-md bg-black/70 px-2 py-1 text-xs text-white backdrop-blur-sm transition-opacity hover:bg-black/80',
          positionClasses[attributionPosition],
          attributionClassName
        )}
      >
        <CameraIcon className="h-3 w-3" />
        <a
          href={`${image.user.links.html}?utm_source=vibe&utm_medium=referral`}
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
          onClick={(e) => e.stopPropagation()}
        >
          {image.user.name}
        </a>
        <span className="text-white/70">on</span>
        <a
          href="https://unsplash.com/?utm_source=vibe&utm_medium=referral"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline"
          onClick={(e) => e.stopPropagation()}
        >
          Unsplash
        </a>
        <ExternalLinkIcon className="h-3 w-3" />
      </div>
    );
  };

  // Loading skeleton
  if (loading) {
    return (
      <div
        className={cn(
          'animate-pulse bg-muted flex items-center justify-center',
          className
        )}
        style={{ width, height }}
      >
        <CameraIcon className="h-8 w-8 text-muted-foreground" />
      </div>
    );
  }

  // Error state or fallback
  if (error || !image) {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <Image
          src={fallbackSrc}
          alt={alt || 'Fallback image'}
          width={width}
          height={height}
          className="object-cover"
          onLoad={handleImageLoad}
          onError={handleImageError}
          priority={priority}
        />
      </div>
    );
  }

  // Main image display
  return (
    <div 
      className={cn('relative overflow-hidden cursor-pointer group', className)}
      onClick={handleImageClick}
    >
      <Image
        src={getImageUrl(image)}
        alt={alt || image.alt_description || image.description || 'Unsplash image'}
        width={width}
        height={height}
        className={cn(
          'object-cover transition-transform duration-300 group-hover:scale-105',
          !imageLoaded && 'opacity-0'
        )}
        onLoad={handleImageLoad}
        onError={handleImageError}
        priority={priority}
        loading={lazy ? 'lazy' : 'eager'}
      />
      
      {/* Loading overlay */}
      {!imageLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse">
          <CameraIcon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      
      {/* Attribution */}
      {showAttribution && imageLoaded && <Attribution image={image} />}
    </div>
  );
}

/**
 * Specialized components for common use cases
 */

// Project thumbnail component
export function ProjectThumbnail({
  projectName,
  content,
  className,
  ...props
}: Omit<UnsplashImageProps, 'context'> & {
  projectName: string;
  content?: string;
}) {
  return (
    <UnsplashImage
      context={{
        type: 'project',
        content: `${projectName} ${content || ''}`,
        mood: 'professional',
        aspectRatio: 'landscape',
      }}
      className={cn('rounded-lg', className)}
      {...props}
    />
  );
}

// Landing hero image component
export function LandingHeroImage({
  theme = 'light',
  className,
  ...props
}: Omit<UnsplashImageProps, 'context'> & {
  theme?: 'light' | 'dark';
}) {
  return (
    <UnsplashImage
      context={{
        type: 'landing',
        category: 'technology innovation',
        mood: theme === 'dark' ? 'dark' : 'light',
        aspectRatio: 'landscape',
      }}
      size="full"
      className={cn('w-full h-full', className)}
      {...props}
    />
  );
}

// Error page background component
export function ErrorPageImage({
  errorType = 'general',
  className,
  ...props
}: Omit<UnsplashImageProps, 'context'> & {
  errorType?: '404' | '500' | 'general';
}) {
  const categories = {
    '404': 'lost path maze',
    '500': 'broken abstract',
    'general': 'calm peaceful'
  };

  return (
    <UnsplashImage
      context={{
        type: 'error',
        category: categories[errorType],
        mood: 'minimal',
        aspectRatio: 'landscape',
      }}
      className={cn('w-full h-full opacity-50', className)}
      showAttribution={false}
      {...props}
    />
  );
}

// Profile placeholder component
export function ProfilePlaceholder({
  username,
  className,
  ...props
}: Omit<UnsplashImageProps, 'context'> & {
  username?: string;
}) {
  return (
    <UnsplashImage
      context={{
        type: 'profile',
        content: username,
        mood: 'minimal',
        aspectRatio: 'square',
      }}
      className={cn('rounded-full', className)}
      size="small"
      {...props}
    />
  );
}
