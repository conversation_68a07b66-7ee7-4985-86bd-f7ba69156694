"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  UnsplashImage, 
  ProjectThumbnail, 
  LandingHeroImage, 
  ErrorPageImage, 
  ProfilePlaceholder 
} from '@/components/unsplash-image';
import { 
  useUnsplashSearch, 
  useContextualImage, 
  useRandomImage,
  useProjectThumbnail,
  useLandingHeroImage,
  useAIContentImages,
  useThemedCollection,
  useCacheStats 
} from '@/hooks/use-unsplash';
import { Loader2, Search, Shuffle, Image as ImageIcon, Palette, Code, Globe, AlertTriangle, User } from 'lucide-react';

export default function UnsplashDemoPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [projectName, setProjectName] = useState('React Dashboard');
  const [aiPrompt, setAIPrompt] = useState('Build a modern e-commerce website');
  const [theme, setTheme] = useState('technology');

  // Hook examples
  const searchResults = useUnsplashSearch(searchQuery, { per_page: 6 }, !!searchQuery);
  const randomImage = useRandomImage('technology', 'landscape');
  const projectThumbnail = useProjectThumbnail(projectName);
  const landingHero = useLandingHeroImage('light');
  const aiImages = useAIContentImages(aiPrompt, 4, !!aiPrompt);
  const themedImages = useThemedCollection(theme, 6);
  const cacheStats = useCacheStats();

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Unsplash Integration Demo</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Explore the comprehensive Unsplash integration with intelligent image selection, 
          caching, and contextual recommendations.
        </p>
        
        {/* Cache Stats */}
        <div className="flex justify-center gap-4">
          <Badge variant="secondary">
            Cache Size: {cacheStats.data?.size || 0}
          </Badge>
          <Badge variant="outline">
            API Calls Saved: {cacheStats.data?.size || 0}
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="search" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </TabsTrigger>
          <TabsTrigger value="contextual" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Contextual
          </TabsTrigger>
          <TabsTrigger value="specialized" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Specialized
          </TabsTrigger>
          <TabsTrigger value="ai" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            AI Content
          </TabsTrigger>
          <TabsTrigger value="themed" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Themed
          </TabsTrigger>
          <TabsTrigger value="components" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Components
          </TabsTrigger>
        </TabsList>

        {/* Search Tab */}
        <TabsContent value="search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Image Search
              </CardTitle>
              <CardDescription>
                Search for images using keywords with intelligent caching
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Search for images..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Button 
                  onClick={() => setSearchQuery('technology')}
                  variant="outline"
                >
                  Try "technology"
                </Button>
              </div>
              
              {searchResults.isLoading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}
              
              {searchResults.data && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {searchResults.data.images.map((image) => (
                    <UnsplashImage
                      key={image.id}
                      imageId={image.id}
                      width={300}
                      height={200}
                      className="rounded-lg"
                      alt={image.alt_description || 'Search result'}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contextual Tab */}
        <TabsContent value="contextual" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Random Technology Image</CardTitle>
                <CardDescription>
                  Automatically selected based on category
                </CardDescription>
              </CardHeader>
              <CardContent>
                {randomImage.isLoading ? (
                  <div className="h-48 bg-muted animate-pulse rounded-lg" />
                ) : randomImage.data ? (
                  <UnsplashImage
                    imageId={randomImage.data.image.id}
                    width={400}
                    height={200}
                    className="rounded-lg w-full"
                  />
                ) : (
                  <div className="h-48 bg-muted rounded-lg flex items-center justify-center">
                    <p className="text-muted-foreground">No image found</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contextual Selection</CardTitle>
                <CardDescription>
                  Smart image selection based on content analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UnsplashImage
                  context={{
                    type: 'project',
                    content: 'modern web development dashboard',
                    mood: 'professional',
                    aspectRatio: 'landscape',
                  }}
                  width={400}
                  height={200}
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Specialized Tab */}
        <TabsContent value="specialized" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Project Thumbnail */}
            <Card>
              <CardHeader>
                <CardTitle>Project Thumbnail</CardTitle>
                <CardDescription>
                  Dynamic thumbnails for projects
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  placeholder="Project name..."
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
                <ProjectThumbnail
                  projectName={projectName}
                  width={300}
                  height={200}
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>

            {/* Landing Hero */}
            <Card>
              <CardHeader>
                <CardTitle>Landing Hero</CardTitle>
                <CardDescription>
                  Hero images for landing pages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <LandingHeroImage
                  theme="light"
                  width={300}
                  height={200}
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>

            {/* Error Page */}
            <Card>
              <CardHeader>
                <CardTitle>Error Background</CardTitle>
                <CardDescription>
                  Calming images for error pages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ErrorPageImage
                  errorType="404"
                  width={300}
                  height={200}
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* AI Content Tab */}
        <TabsContent value="ai" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                AI Content Images
              </CardTitle>
              <CardDescription>
                Images selected based on AI-generated content prompts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Describe what you want to build..."
                  value={aiPrompt}
                  onChange={(e) => setAIPrompt(e.target.value)}
                />
                <Button 
                  onClick={() => setAIPrompt('Build a social media app')}
                  variant="outline"
                >
                  Try Example
                </Button>
              </div>
              
              {aiImages.isLoading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}
              
              {aiImages.data && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {aiImages.data.images.map((image) => (
                    <UnsplashImage
                      key={image.id}
                      imageId={image.id}
                      width={200}
                      height={150}
                      className="rounded-lg"
                      alt={`AI content for: ${aiPrompt}`}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Themed Tab */}
        <TabsContent value="themed" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Themed Collections
              </CardTitle>
              <CardDescription>
                Curated image collections by theme
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2 flex-wrap">
                {['technology', 'nature', 'business', 'abstract', 'minimal'].map((t) => (
                  <Button
                    key={t}
                    variant={theme === t ? 'default' : 'outline'}
                    onClick={() => setTheme(t)}
                    size="sm"
                  >
                    {t}
                  </Button>
                ))}
              </div>
              
              {themedImages.isLoading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}
              
              {themedImages.data && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {themedImages.data.images.map((image) => (
                    <UnsplashImage
                      key={image.id}
                      imageId={image.id}
                      width={250}
                      height={180}
                      className="rounded-lg"
                      alt={`${theme} themed image`}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Components Tab */}
        <TabsContent value="components" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Profile Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Avatar</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center">
                <ProfilePlaceholder
                  username="johndoe"
                  width={100}
                  height={100}
                  className="rounded-full"
                />
              </CardContent>
            </Card>

            {/* Small Thumbnail */}
            <Card>
              <CardHeader>
                <CardTitle>Small Thumbnail</CardTitle>
              </CardHeader>
              <CardContent>
                <UnsplashImage
                  query="coding"
                  width={150}
                  height={100}
                  size="small"
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>

            {/* Square Format */}
            <Card>
              <CardHeader>
                <CardTitle>Square Format</CardTitle>
              </CardHeader>
              <CardContent>
                <UnsplashImage
                  context={{
                    type: 'placeholder',
                    mood: 'minimal',
                    aspectRatio: 'square',
                  }}
                  width={150}
                  height={150}
                  className="rounded-lg w-full"
                />
              </CardContent>
            </Card>

            {/* With Attribution */}
            <Card>
              <CardHeader>
                <CardTitle>With Attribution</CardTitle>
              </CardHeader>
              <CardContent>
                <UnsplashImage
                  query="workspace"
                  width={150}
                  height={100}
                  className="rounded-lg w-full"
                  showAttribution={true}
                  attributionPosition="bottom-left"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
