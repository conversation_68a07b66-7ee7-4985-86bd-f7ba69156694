import React, { useState, useEffect } from 'react';
import { GenerationProgress, GenerationStep } from './GenerationProgress';
import { EnhancedFileTree, FileNode } from './EnhancedFileTree';
import { CodeStreamWriter, codeTemplates } from './CodeStreamWriter';
import { NpmInstallVisualization, PackageInfo } from './NpmInstallVisualization';
import { ProjectTemplate, getProjectTemplate } from '../lib/projectTemplates';
import { Play, Pause, RotateCcw, Download, ExternalLink } from 'lucide-react';

interface ProjectGeneratorProps {
  templateId: string;
  projectName: string;
  onComplete?: () => void;
  className?: string;
}

export const ProjectGenerator: React.FC<ProjectGeneratorProps> = ({
  templateId,
  projectName,
  onComplete,
  className = ""
}) => {
  const [template, setTemplate] = useState<ProjectTemplate | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('');
  const [steps, setSteps] = useState<GenerationStep[]>([]);
  const [files, setFiles] = useState<FileNode[]>([]);
  const [packages, setPackages] = useState<PackageInfo[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showInstallation, setShowInstallation] = useState(false);
  const [generationPhase, setGenerationPhase] = useState<'setup' | 'install' | 'files' | 'complete'>('setup');

  useEffect(() => {
    const projectTemplate = getProjectTemplate(templateId);
    if (projectTemplate) {
      setTemplate(projectTemplate);
      setSteps([...projectTemplate.steps]);
      setFiles([...projectTemplate.files]);
      setPackages([...projectTemplate.packages]);
    }
  }, [templateId]);

  const startGeneration = async () => {
    if (!template) return;
    
    setIsGenerating(true);
    setGenerationPhase('setup');

    // Phase 1: Setup and Config
    await executeSetupPhase();
    
    // Phase 2: Install Dependencies
    setGenerationPhase('install');
    setShowInstallation(true);
    await executeInstallPhase();
    
    // Phase 3: Generate Files
    setGenerationPhase('files');
    setShowInstallation(false);
    await executeFileGeneration();
    
    // Phase 4: Complete
    setGenerationPhase('complete');
    setIsGenerating(false);
    onComplete?.();
  };

  const executeSetupPhase = async () => {
    const setupSteps = steps.filter(step => step.type === 'setup' || step.type === 'config');
    
    for (const step of setupSteps) {
      setCurrentStep(step.id);
      updateStepStatus(step.id, 'generating');
      
      // Simulate setup time
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      updateStepStatus(step.id, 'complete');
      updateFileStatus(step.file, 'complete');
    }
  };

  const executeInstallPhase = async () => {
    const installStep = steps.find(step => step.type === 'install');
    if (installStep) {
      setCurrentStep(installStep.id);
      updateStepStatus(installStep.id, 'generating');
      
      // Wait for npm installation to complete
      await new Promise(resolve => {
        const timer = setTimeout(resolve, 8000); // 8 seconds for installation
        return timer;
      });
      
      updateStepStatus(installStep.id, 'complete');
      updateFileStatus('package-lock.json', 'complete');
    }
  };

  const executeFileGeneration = async () => {
    const fileSteps = steps.filter(step => step.type === 'file');
    
    for (const step of fileSteps) {
      setCurrentStep(step.id);
      updateStepStatus(step.id, 'generating');
      updateFileStatus(step.file, 'generating');
      
      // Simulate file generation
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500));
      
      updateStepStatus(step.id, 'complete');
      updateFileStatus(step.file, 'complete');
    }
  };

  const updateStepStatus = (stepId: string, status: GenerationStep['status']) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status, duration: status === 'complete' ? Math.floor(Math.random() * 2000 + 500) : undefined }
        : step
    ));
  };

  const updateFileStatus = (fileName: string | undefined, status: FileNode['status']) => {
    if (!fileName) return;
    
    const updateNodeStatus = (nodes: FileNode[]): FileNode[] => {
      return nodes.map(node => {
        if (node.type === 'file' && (node.name === fileName || node.path.endsWith(fileName))) {
          return { ...node, status };
        }
        if (node.children) {
          return { ...node, children: updateNodeStatus(node.children) };
        }
        return node;
      });
    };
    
    setFiles(prev => updateNodeStatus(prev));
  };

  const handleFileSelect = (file: FileNode) => {
    setSelectedFile(file);
  };

  const resetGeneration = () => {
    if (!template) return;
    
    setIsGenerating(false);
    setCurrentStep('');
    setGenerationPhase('setup');
    setShowInstallation(false);
    setSelectedFile(null);
    setSteps([...template.steps]);
    setFiles([...template.files]);
    setPackages([...template.packages]);
  };

  const getFileContent = (file: FileNode): string => {
    // Generate content based on file type
    switch (file.name) {
      case 'package.json':
        return codeTemplates['package.json'](projectName);
      case 'tsconfig.json':
        return codeTemplates['tsconfig.json']();
      case 'vite.config.ts':
        return codeTemplates['vite.config.ts']();
      case 'tailwind.config.js':
        return codeTemplates['tailwind.config.js']();
      case 'Header.tsx':
        return generateHeaderComponent();
      case 'Hero.tsx':
        return generateHeroComponent();
      case 'App.tsx':
        return generateAppComponent();
      default:
        return `// ${file.name}\n// Generated by Bolt AI\n\nexport default function Component() {\n  return (\n    <div>\n      <h1>Hello from ${file.name}</h1>\n    </div>\n  );\n}`;
    }
  };

  const generateHeaderComponent = () => `import React, { useState } from 'react';
import { Menu, X, ChevronDown } from 'lucide-react';

export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { name: 'Home', href: '#home' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'About', href: '#about' },
    { name: 'Contact', href: '#contact' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold text-gray-900">
              ${projectName}
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:flex">
            <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Get Started
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-gray-900 p-2"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <button className="w-full mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Get Started
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};`;

  const generateHeroComponent = () => `import React from 'react';
import { ArrowRight, Play } from 'lucide-react';

export const Hero: React.FC = () => {
  return (
    <section id="home" className="pt-16 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                Creative Digital
                <span className="text-blue-600 block">Solutions</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                We craft beautiful, functional, and user-centered digital experiences 
                that help your business grow and succeed in the digital world.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 text-lg font-medium">
                Get Started
                <ArrowRight size={20} />
              </button>
              <button className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-lg hover:border-gray-400 transition-colors flex items-center justify-center gap-2 text-lg font-medium">
                <Play size={20} />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">150+</div>
                <div className="text-gray-600">Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">50+</div>
                <div className="text-gray-600">Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900">5+</div>
                <div className="text-gray-600">Years</div>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg"></div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-16 bg-gray-100 rounded"></div>
                  <div className="h-16 bg-gray-100 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};`;

  const generateAppComponent = () => `import React from 'react';
import { Header } from './components/Header';
import { Hero } from './components/Hero';
import { Services } from './components/Services';
import { Portfolio } from './components/Portfolio';
import { About } from './components/About';
import { Contact } from './components/Contact';
import { Footer } from './components/Footer';
import './styles/globals.css';

function App() {
  return (
    <div className="App">
      <Header />
      <main>
        <Hero />
        <Services />
        <Portfolio />
        <About />
        <Contact />
      </main>
      <Footer />
    </div>
  );
}

export default App;`;

  if (!template) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Template not found</h3>
          <p className="text-gray-600">The requested project template could not be loaded.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{template.name}</h2>
            <p className="text-gray-600 mt-1">{template.description}</p>
          </div>
          <div className="flex gap-2">
            {!isGenerating && generationPhase === 'setup' && (
              <button
                onClick={startGeneration}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <Play size={16} />
                Start Generation
              </button>
            )}
            {generationPhase === 'complete' && (
              <>
                <button
                  onClick={resetGeneration}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
                >
                  <RotateCcw size={16} />
                  Reset
                </button>
                <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2">
                  <Download size={16} />
                  Download
                </button>
                <button className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2">
                  <ExternalLink size={16} />
                  Preview
                </button>
              </>
            )}
          </div>
        </div>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2">
          {template.technologies.map(tech => (
            <span key={tech} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
              {tech}
            </span>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Generation Progress */}
          <GenerationProgress
            steps={steps}
            currentStep={currentStep}
            onStepComplete={(stepId) => console.log('Step completed:', stepId)}
          />

          {/* NPM Installation */}
          {showInstallation && (
            <NpmInstallVisualization
              packages={packages}
              onInstallComplete={() => console.log('Installation complete')}
            />
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* File Tree */}
          <EnhancedFileTree
            files={files}
            onFileSelect={handleFileSelect}
            selectedFile={selectedFile?.id}
          />

          {/* Code Viewer */}
          {selectedFile && selectedFile.status === 'complete' && (
            <CodeStreamWriter
              content={getFileContent(selectedFile)}
              language="typescript"
              fileName={selectedFile.name}
              onComplete={() => console.log('Code writing complete')}
              speed={80}
              autoStart={true}
            />
          )}
        </div>
      </div>
    </div>
  );
};
