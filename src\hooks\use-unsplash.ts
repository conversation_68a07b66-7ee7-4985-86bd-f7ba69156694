"use client";

import { useTRPC } from '@/trpc/client';
import { ImageContext } from '@/lib/unsplash-patterns';
import { UnsplashImage } from '@/lib/unsplash';

/**
 * Hook for searching Unsplash images
 */
export function useUnsplashSearch(
  query: string,
  options?: {
    orientation?: 'landscape' | 'portrait' | 'squarish';
    color?: 'black_and_white' | 'black' | 'white' | 'yellow' | 'orange' | 'red' | 'purple' | 'magenta' | 'green' | 'teal' | 'blue';
    per_page?: number;
    page?: number;
  },
  enabled = true
) {
  return useTRPC.images.search.useQuery(
    {
      query,
      ...options,
    },
    {
      enabled: enabled && !!query,
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
    }
  );
}

/**
 * Hook for getting contextual images
 */
export function useContextualImage(
  context: ImageContext,
  enabled = true
) {
  return useTRPC.images.contextual.useQuery(
    context,
    {
      enabled,
      staleTime: 1000 * 60 * 10, // 10 minutes
      cacheTime: 1000 * 60 * 60, // 1 hour
    }
  );
}

/**
 * Hook for getting random images
 */
export function useRandomImage(
  category?: string,
  orientation?: 'landscape' | 'portrait' | 'squarish',
  enabled = true
) {
  return useTRPC.images.random.useQuery(
    {
      category,
      orientation,
    },
    {
      enabled,
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
    }
  );
}

/**
 * Hook for getting image by ID
 */
export function useImageById(id: string, enabled = true) {
  return useTRPC.images.byId.useQuery(
    { id },
    {
      enabled: enabled && !!id,
      staleTime: 1000 * 60 * 60, // 1 hour
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    }
  );
}

/**
 * Hook for tracking image downloads
 */
export function useTrackDownload() {
  return useTRPC.images.trackDownload.useMutation();
}

/**
 * Specialized hooks for common use cases
 */

/**
 * Hook for project thumbnails
 */
export function useProjectThumbnail(
  projectName: string,
  content?: string,
  enabled = true
) {
  return useTRPC.images.projectThumbnail.useQuery(
    {
      projectName,
      content,
    },
    {
      enabled: enabled && !!projectName,
      staleTime: 1000 * 60 * 30, // 30 minutes
      cacheTime: 1000 * 60 * 60 * 2, // 2 hours
    }
  );
}

/**
 * Hook for landing hero images
 */
export function useLandingHeroImage(
  theme: 'light' | 'dark' = 'light',
  enabled = true
) {
  return useTRPC.images.landingHero.useQuery(
    { theme },
    {
      enabled,
      staleTime: 1000 * 60 * 60, // 1 hour
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    }
  );
}

/**
 * Hook for error page backgrounds
 */
export function useErrorBackground(
  errorType: '404' | '500' | 'general' = 'general',
  enabled = true
) {
  return useTRPC.images.errorBackground.useQuery(
    { errorType },
    {
      enabled,
      staleTime: 1000 * 60 * 60, // 1 hour
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    }
  );
}

/**
 * Hook for profile placeholders
 */
export function useProfilePlaceholder(
  username?: string,
  enabled = true
) {
  return useTRPC.images.profilePlaceholder.useQuery(
    { username },
    {
      enabled,
      staleTime: 1000 * 60 * 60, // 1 hour
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    }
  );
}

/**
 * Hook for fragment preview images
 */
export function useFragmentPreview(
  content: string,
  title?: string,
  enabled = true
) {
  return useTRPC.images.fragmentPreview.useQuery(
    {
      content,
      title,
    },
    {
      enabled: enabled && !!content,
      staleTime: 1000 * 60 * 15, // 15 minutes
      cacheTime: 1000 * 60 * 60, // 1 hour
    }
  );
}

/**
 * Hook for AI content images
 */
export function useAIContentImages(
  prompt: string,
  count = 3,
  enabled = true
) {
  return useTRPC.images.aiContentImages.useQuery(
    {
      prompt,
      count,
    },
    {
      enabled: enabled && !!prompt,
      staleTime: 1000 * 60 * 10, // 10 minutes
      cacheTime: 1000 * 60 * 60, // 1 hour
    }
  );
}

/**
 * Hook for themed collections
 */
export function useThemedCollection(
  theme: string,
  count = 5,
  enabled = true
) {
  return useTRPC.images.themedCollection.useQuery(
    {
      theme,
      count,
    },
    {
      enabled: enabled && !!theme,
      staleTime: 1000 * 60 * 30, // 30 minutes
      cacheTime: 1000 * 60 * 60 * 2, // 2 hours
    }
  );
}

/**
 * Hook for cache management (debugging)
 */
export function useCacheStats() {
  return useTRPC.images.cacheStats.useQuery(undefined, {
    staleTime: 1000 * 10, // 10 seconds
    cacheTime: 1000 * 30, // 30 seconds
  });
}

export function useClearCache() {
  return useTRPC.images.clearCache.useMutation();
}

/**
 * Utility hook for image loading states
 */
export function useImageLoader() {
  const trackDownload = useTrackDownload();

  const handleImageClick = async (image: UnsplashImage) => {
    if (image.links.download_location) {
      try {
        await trackDownload.mutateAsync({
          downloadLocation: image.links.download_location,
        });
      } catch (error) {
        console.warn('Failed to track download:', error);
      }
    }
  };

  return {
    handleImageClick,
    isTracking: trackDownload.isLoading,
  };
}

/**
 * Hook for smart image selection based on content analysis
 */
export function useSmartImageSelection(
  content: string,
  type: ImageContext['type'] = 'placeholder',
  enabled = true
) {
  // Extract keywords and determine best context
  const context: ImageContext = {
    type,
    content,
    mood: 'professional',
    aspectRatio: 'landscape',
  };

  // Analyze content for better context
  if (content) {
    const lowerContent = content.toLowerCase();
    
    // Determine mood based on content
    if (lowerContent.includes('dark') || lowerContent.includes('night')) {
      context.mood = 'dark';
    } else if (lowerContent.includes('bright') || lowerContent.includes('light')) {
      context.mood = 'light';
    } else if (lowerContent.includes('minimal') || lowerContent.includes('simple')) {
      context.mood = 'minimal';
    } else if (lowerContent.includes('creative') || lowerContent.includes('artistic')) {
      context.mood = 'creative';
    } else if (lowerContent.includes('vibrant') || lowerContent.includes('colorful')) {
      context.mood = 'vibrant';
    }

    // Determine aspect ratio based on content
    if (lowerContent.includes('portrait') || lowerContent.includes('vertical')) {
      context.aspectRatio = 'portrait';
    } else if (lowerContent.includes('square') || lowerContent.includes('avatar')) {
      context.aspectRatio = 'square';
    }
  }

  return useContextualImage(context, enabled);
}

/**
 * Hook for batch image loading
 */
export function useBatchImages(
  queries: string[],
  enabled = true
) {
  const results = queries.map(query => 
    useUnsplashSearch(query, { per_page: 1 }, enabled && !!query)
  );

  const isLoading = results.some(result => result.isLoading);
  const isError = results.some(result => result.isError);
  const images = results
    .map(result => result.data?.images[0])
    .filter((image): image is UnsplashImage => !!image);

  return {
    images,
    isLoading,
    isError,
    results,
  };
}
