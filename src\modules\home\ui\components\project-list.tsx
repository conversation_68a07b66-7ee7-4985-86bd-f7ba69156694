"use client";

import Link from "next/link";
import Image from "next/image";
import {formatDistanceToNow} from "date-fns";
import {useQuery} from "@tanstack/react-query";
import { useTRPC } from "@/trpc/client";
import { Button } from "@/components/ui/button";
import { useUser } from "@clerk/nextjs";
import { ProjectThumbnail } from "@/components/unsplash-image";


export const ProjectList = () => {
    const {user} = useUser();
    const trpc = useTRPC();
    const {data: projects} = useQuery(trpc.projects.getMany.queryOptions());

    if (!user) return null;

    return (
        <div className="w-full bg-white dark:bg-sidebar rounded-xl p-8 border flex flex-col gap-y-6 sm:gap-y-4">
            <h2 className="text-2xl font-semibold">
                {user?.firstName}&apos;s Vibes
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                {projects?.length ===0 && (
                    <div className="col-span-full text-center">
                        <p className="text-sm text-muted-foreground">
                            No projects found
                        </p>
                    </div>
                )}
                {projects?.map((project)=> (
                    <Button
                    key = {project.id}
                    variant = "outline"
                    className="font-normal h-auto justify-start w-full text-start p-4"
                    asChild
                    >
                        <Link href={`/projects/${project.id}`}>
                        <div className="flex items-center gap-x-4">
                            <div className="w-8 h-8 rounded-md overflow-hidden flex-shrink-0">
                                <ProjectThumbnail
                                    projectName={project.name}
                                    width={32}
                                    height={32}
                                    size="thumb"
                                    className="w-full h-full"
                                    showAttribution={false}
                                    fallbackSrc="/logo.svg"
                                    alt={`${project.name} thumbnail`}
                                />
                            </div>
                            <div className="flex flex-col min-w-0 flex-1">
                                <h3 className="truncate font-medium">
                                    {project.name}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                    {formatDistanceToNow(project.updatedAt, {
                                        addSuffix: true,
                                    })}
                                </p>
                            </div>

                        </div>
                        </Link>

                    </Button>
                ))}
            </div>
        </div>

    )
}