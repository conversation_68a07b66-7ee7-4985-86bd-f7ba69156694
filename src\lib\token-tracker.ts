import { prisma } from "./db";

export interface TokenUsageData {
  modelName: string;
  agentName: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  estimatedCost?: number;
  requestDuration?: number;
}

export interface TokenTrackingResult {
  usage: TokenUsageData;
  result: any;
}

/**
 * Logs token usage to console and terminal with detailed formatting
 */
export function logTokenUsage(usage: TokenUsageData, context?: string) {
  const contextStr = context ? `[${context}] ` : '';
  const separator = '='.repeat(60);
  
  console.log(`\n${separator}`);
  console.log(`${contextStr}🤖 AI MODEL TOKEN USAGE`);
  console.log(`${separator}`);
  console.log(`📊 Agent: ${usage.agentName}`);
  console.log(`🔧 Model: ${usage.modelName}`);
  console.log(`📥 Input Tokens: ${usage.inputTokens.toLocaleString()}`);
  console.log(`📤 Output Tokens: ${usage.outputTokens.toLocaleString()}`);
  console.log(`📊 Total Tokens: ${usage.totalTokens.toLocaleString()}`);
  
  if (usage.estimatedCost) {
    console.log(`💰 Estimated Cost: $${usage.estimatedCost.toFixed(4)}`);
  }
  
  if (usage.requestDuration) {
    console.log(`⏱️  Duration: ${usage.requestDuration}ms`);
  }
  
  console.log(`${separator}\n`);
}

/**
 * Saves token usage to database
 */
export async function saveTokenUsage(
  projectId: string,
  usage: TokenUsageData,
  messageId?: string
) {
  try {
    const tokenUsage = await prisma.tokenUsage.create({
      data: {
        projectId,
        messageId,
        modelName: usage.modelName,
        agentName: usage.agentName,
        inputTokens: usage.inputTokens,
        outputTokens: usage.outputTokens,
        totalTokens: usage.totalTokens,
        estimatedCost: usage.estimatedCost,
        requestDuration: usage.requestDuration,
      },
    });
    
    console.log(`✅ Token usage saved to database (ID: ${tokenUsage.id})`);
    return tokenUsage;
  } catch (error) {
    console.error('❌ Failed to save token usage to database:', error);
    throw error;
  }
}

/**
 * Retrieves token usage statistics for a project
 */
export async function getProjectTokenUsage(projectId: string) {
  try {
    const usages = await prisma.tokenUsage.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' },
    });

    const totalStats = usages.reduce(
      (acc, usage) => ({
        totalInputTokens: acc.totalInputTokens + usage.inputTokens,
        totalOutputTokens: acc.totalOutputTokens + usage.outputTokens,
        totalTokens: acc.totalTokens + usage.totalTokens,
        totalCost: acc.totalCost + (usage.estimatedCost || 0),
        requestCount: acc.requestCount + 1,
      }),
      {
        totalInputTokens: 0,
        totalOutputTokens: 0,
        totalTokens: 0,
        totalCost: 0,
        requestCount: 0,
      }
    );

    return {
      usages,
      stats: totalStats,
    };
  } catch (error) {
    console.error('❌ Failed to retrieve token usage:', error);
    throw error;
  }
}

/**
 * Retrieves token usage statistics for a specific message
 */
export async function getMessageTokenUsage(messageId: string) {
  try {
    const usages = await prisma.tokenUsage.findMany({
      where: { messageId },
      orderBy: { createdAt: 'asc' },
    });

    return usages;
  } catch (error) {
    console.error('❌ Failed to retrieve message token usage:', error);
    throw error;
  }
}

/**
 * Estimates token count from text (rough approximation)
 * GPT models typically use ~4 characters per token on average
 */
export function estimateTokenCount(text: string): number {
  if (!text) return 0;
  // Rough estimation: 4 characters per token
  return Math.ceil(text.length / 4);
}

/**
 * Estimates cost based on model and token usage
 * These are approximate costs - update with actual pricing
 */
export function estimateTokenCost(modelName: string, inputTokens: number, outputTokens: number): number {
  const pricing: Record<string, { input: number; output: number }> = {
    'gpt-4o': { input: 0.005 / 1000, output: 0.015 / 1000 }, // $5/1M input, $15/1M output
    'gpt-4.1': { input: 0.03 / 1000, output: 0.06 / 1000 }, // $30/1M input, $60/1M output
    'gpt-4': { input: 0.03 / 1000, output: 0.06 / 1000 }, // fallback for gpt-4 variants
    'gpt-3.5-turbo': { input: 0.001 / 1000, output: 0.002 / 1000 }, // $1/1M input, $2/1M output
  };

  const modelPricing = pricing[modelName] || pricing['gpt-4']; // fallback to gpt-4 pricing
  return (inputTokens * modelPricing.input) + (outputTokens * modelPricing.output);
}

/**
 * Tracks token usage for agent runs by estimating from input/output text
 */
export async function trackAgentTokenUsage(
  projectId: string,
  agentName: string,
  modelName: string,
  inputText: string,
  outputText: string,
  messageId?: string,
  duration?: number
): Promise<TokenUsageData> {
  const inputTokens = estimateTokenCount(inputText);
  const outputTokens = estimateTokenCount(outputText);
  const totalTokens = inputTokens + outputTokens;
  const estimatedCost = estimateTokenCost(modelName, inputTokens, outputTokens);

  const usage: TokenUsageData = {
    modelName,
    agentName,
    inputTokens,
    outputTokens,
    totalTokens,
    estimatedCost,
    requestDuration: duration,
  };

  // Log to console
  logTokenUsage(usage, 'ESTIMATED');

  // Save to database (async, don't wait)
  saveTokenUsage(projectId, usage, messageId).catch(error => {
    console.error('Failed to save token usage:', error);
  });

  return usage;
}

/**
 * Creates a custom OpenAI model wrapper that tracks token usage
 */
export function createTrackedOpenAIModel(
  projectId: string,
  agentName: string,
  modelConfig: any,
  messageId?: string
) {
  const originalModel = modelConfig;

  return {
    ...originalModel,
    async generate(input: any, options?: any) {
      const startTime = Date.now();

      try {
        console.log(`🚀 Starting AI request - Agent: ${agentName}, Model: ${originalModel.model || 'unknown'}`);

        const result = await originalModel.generate(input, options);

        const endTime = Date.now();
        const duration = endTime - startTime;

        // Extract token usage from result
        let inputTokens = 0;
        let outputTokens = 0;

        // Try to extract token usage from the result
        if (result && typeof result === 'object') {
          const resultObj = result as any;

          // Check for usage in various possible locations
          if (resultObj.usage) {
            inputTokens = resultObj.usage.prompt_tokens || resultObj.usage.input_tokens || 0;
            outputTokens = resultObj.usage.completion_tokens || resultObj.usage.output_tokens || 0;
          } else if (resultObj.response?.usage) {
            inputTokens = resultObj.response.usage.prompt_tokens || resultObj.response.usage.input_tokens || 0;
            outputTokens = resultObj.response.usage.completion_tokens || resultObj.response.usage.output_tokens || 0;
          } else if (resultObj.meta?.usage) {
            inputTokens = resultObj.meta.usage.prompt_tokens || resultObj.meta.usage.input_tokens || 0;
            outputTokens = resultObj.meta.usage.completion_tokens || resultObj.meta.usage.output_tokens || 0;
          }
        }

        const totalTokens = inputTokens + outputTokens;
        const estimatedCost = estimateTokenCost(originalModel.model || 'gpt-4o', inputTokens, outputTokens);

        const usage: TokenUsageData = {
          modelName: originalModel.model || 'gpt-4o',
          agentName,
          inputTokens,
          outputTokens,
          totalTokens,
          estimatedCost,
          requestDuration: duration,
        };

        // Log to console
        logTokenUsage(usage, 'SUCCESS');

        // Save to database (async, don't wait)
        saveTokenUsage(projectId, usage, messageId).catch(error => {
          console.error('Failed to save token usage:', error);
        });

        return result;
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        console.error(`❌ AI request failed - Agent: ${agentName}, Model: ${originalModel.model || 'unknown'}, Duration: ${duration}ms`);
        console.error('Error:', error);

        throw error;
      }
    }
  };
}

/**
 * Wrapper function to track token usage for any AI model call
 */
export async function trackTokenUsage<T>(
  projectId: string,
  agentName: string,
  modelName: string,
  aiFunction: () => Promise<T>,
  messageId?: string
): Promise<TokenTrackingResult> {
  const startTime = Date.now();

  try {
    console.log(`🚀 Starting AI request - Agent: ${agentName}, Model: ${modelName}`);

    const result = await aiFunction();

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Extract token usage from result if available
    // This will need to be adapted based on how your AI library returns token info
    let inputTokens = 0;
    let outputTokens = 0;

    // Try to extract token usage from the result
    if (result && typeof result === 'object') {
      const resultObj = result as any;

      // Check common token usage patterns
      if (resultObj.usage) {
        inputTokens = resultObj.usage.prompt_tokens || resultObj.usage.input_tokens || 0;
        outputTokens = resultObj.usage.completion_tokens || resultObj.usage.output_tokens || 0;
      } else if (resultObj.tokens) {
        inputTokens = resultObj.tokens.input || 0;
        outputTokens = resultObj.tokens.output || 0;
      }
    }

    const totalTokens = inputTokens + outputTokens;
    const estimatedCost = estimateTokenCost(modelName, inputTokens, outputTokens);

    const usage: TokenUsageData = {
      modelName,
      agentName,
      inputTokens,
      outputTokens,
      totalTokens,
      estimatedCost,
      requestDuration: duration,
    };

    // Log to console
    logTokenUsage(usage, 'SUCCESS');

    // Save to database
    await saveTokenUsage(projectId, usage, messageId);

    return { usage, result };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error(`❌ AI request failed - Agent: ${agentName}, Model: ${modelName}, Duration: ${duration}ms`);
    console.error('Error:', error);

    throw error;
  }
}
