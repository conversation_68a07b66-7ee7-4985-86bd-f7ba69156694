import { ProjectForm } from "@/modules/home/<USER>/components/project-form";
import { ProjectList } from "@/modules/home/<USER>/components/project-list";
import { LandingHeroImage } from "@/components/unsplash-image";
import Image from "next/image"

const Page = () => {
  return (
    <div className="flex flex-col max-w-5xl mx-auto w-full">
      {/* Hero Section with Background */}
      <section className="relative space-y-6 py-[16vh] 2xl:py-48 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 -z-10">
          <LandingHeroImage
            theme="light"
            className="w-full h-full opacity-10"
            width={1920}
            height={1080}
            size="full"
            showAttribution={false}
            priority={true}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-background/50 via-background/80 to-background" />
        </div>

        {/* Content */}
        <div className="relative z-10">
          <div className="flex flex-col items-center">
            <Image
            src="/logo.svg"
            alt="Vibe"
            width={50}
            height={50}
            className="hidden md:block"
            />
          </div>
          <h1 className="text-2xl md:text-5xl font-bold text-center">
            Build something with vibe
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground text-center">
            Create apps and websites by chatting with AI
          </p>
          <div className="max-w-3xl mx-auto w-full">
            <ProjectForm />
          </div>
        </div>
      </section>
      <ProjectList />
    </div>
  )
}

export default Page;