import React, { useState, useEffect } from 'react';
import { 
  ChevronRight, 
  ChevronDown, 
  File, 
  Folder, 
  FolderOpen,
  FileText,
  Settings,
  Package,
  Code,
  Image,
  Globe,
  Lock,
  GitBranch,
  Loader2,
  CheckCircle,
  Edit3
} from 'lucide-react';

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileNode[];
  status: 'pending' | 'generating' | 'writing' | 'complete' | 'ready';
  size?: number;
  lastModified?: Date;
  content?: string;
  isOpen?: boolean;
}

interface EnhancedFileTreeProps {
  files: FileNode[];
  onFileSelect?: (file: FileNode) => void;
  selectedFile?: string;
  generatingFiles?: Set<string>;
  className?: string;
}

const getFileIcon = (fileName: string, isFolder: boolean, isOpen: boolean = false) => {
  const iconClass = "w-4 h-4";
  
  if (isFolder) {
    return isOpen ? 
      <FolderOpen className={`${iconClass} text-blue-500`} /> : 
      <Folder className={`${iconClass} text-blue-600`} />;
  }

  // File type detection
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'tsx':
    case 'ts':
    case 'jsx':
    case 'js':
      return <Code className={`${iconClass} text-blue-500`} />;
    case 'json':
      return <Settings className={`${iconClass} text-yellow-600`} />;
    case 'css':
    case 'scss':
    case 'sass':
      return <FileText className={`${iconClass} text-pink-500`} />;
    case 'html':
      return <Globe className={`${iconClass} text-orange-500`} />;
    case 'md':
      return <FileText className={`${iconClass} text-gray-600`} />;
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'svg':
    case 'gif':
      return <Image className={`${iconClass} text-green-500`} />;
    case 'lock':
      return <Lock className={`${iconClass} text-gray-500`} />;
    case 'gitignore':
      return <GitBranch className={`${iconClass} text-orange-600`} />;
    default:
      if (fileName === 'package.json' || fileName === 'package-lock.json') {
        return <Package className={`${iconClass} text-red-500`} />;
      }
      return <File className={`${iconClass} text-gray-500`} />;
  }
};

const getStatusIcon = (status: string) => {
  const iconClass = "w-3 h-3";
  
  switch (status) {
    case 'generating':
      return <Loader2 className={`${iconClass} text-blue-500 animate-spin`} />;
    case 'writing':
      return <Edit3 className={`${iconClass} text-green-500 animate-pulse`} />;
    case 'complete':
      return <CheckCircle className={`${iconClass} text-green-500`} />;
    default:
      return null;
  }
};

const FileTreeNode: React.FC<{
  node: FileNode;
  level: number;
  onFileSelect?: (file: FileNode) => void;
  selectedFile?: string;
  onToggle?: (nodeId: string) => void;
}> = ({ node, level, onFileSelect, selectedFile, onToggle }) => {
  const isSelected = selectedFile === node.id;
  const hasChildren = node.children && node.children.length > 0;
  const isFolder = node.type === 'folder';
  
  const handleClick = () => {
    if (isFolder) {
      onToggle?.(node.id);
    } else {
      onFileSelect?.(node);
    }
  };

  const getFileSize = (size?: number) => {
    if (!size) return '';
    if (size < 1024) return `${size}B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`;
    return `${(size / (1024 * 1024)).toFixed(1)}MB`;
  };

  return (
    <div>
      <div
        className={`
          flex items-center gap-2 px-2 py-1.5 rounded cursor-pointer
          hover:bg-gray-100 transition-colors duration-150
          ${isSelected ? 'bg-blue-50 border-l-2 border-blue-500' : ''}
          ${node.status === 'generating' ? 'bg-blue-50' : ''}
          ${node.status === 'writing' ? 'bg-green-50' : ''}
        `}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        {/* Expand/Collapse Icon */}
        {isFolder && hasChildren && (
          <button className="p-0.5 hover:bg-gray-200 rounded">
            {node.isOpen ? (
              <ChevronDown className="w-3 h-3 text-gray-500" />
            ) : (
              <ChevronRight className="w-3 h-3 text-gray-500" />
            )}
          </button>
        )}
        
        {/* File/Folder Icon */}
        <div className="flex-shrink-0">
          {getFileIcon(node.name, isFolder, node.isOpen)}
        </div>
        
        {/* File Name */}
        <span className={`
          flex-1 text-sm truncate
          ${isSelected ? 'font-medium text-blue-700' : 'text-gray-700'}
          ${node.status === 'generating' ? 'text-blue-600' : ''}
          ${node.status === 'writing' ? 'text-green-600' : ''}
        `}>
          {node.name}
        </span>
        
        {/* Status Icon */}
        <div className="flex-shrink-0">
          {getStatusIcon(node.status)}
        </div>
        
        {/* File Size */}
        {!isFolder && node.size && (
          <span className="text-xs text-gray-400 ml-2">
            {getFileSize(node.size)}
          </span>
        )}
      </div>
      
      {/* Children */}
      {isFolder && node.isOpen && hasChildren && (
        <div>
          {node.children!.map(child => (
            <FileTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              selectedFile={selectedFile}
              onToggle={onToggle}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const EnhancedFileTree: React.FC<EnhancedFileTreeProps> = ({
  files,
  onFileSelect,
  selectedFile,
  generatingFiles,
  className = ""
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root', 'src', 'components']));
  const [fileTree, setFileTree] = useState<FileNode[]>(files);

  useEffect(() => {
    setFileTree(files);
  }, [files]);

  const handleToggle = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });

    // Update the isOpen property in the file tree
    const updateNodeOpen = (nodes: FileNode[]): FileNode[] => {
      return nodes.map(node => ({
        ...node,
        isOpen: expandedNodes.has(node.id),
        children: node.children ? updateNodeOpen(node.children) : undefined
      }));
    };

    setFileTree(updateNodeOpen(fileTree));
  };

  const totalFiles = React.useMemo(() => {
    const countFiles = (nodes: FileNode[]): number => {
      return nodes.reduce((count, node) => {
        if (node.type === 'file') {
          return count + 1;
        }
        return count + (node.children ? countFiles(node.children) : 0);
      }, 0);
    };
    return countFiles(fileTree);
  }, [fileTree]);

  const completedFiles = React.useMemo(() => {
    const countCompleted = (nodes: FileNode[]): number => {
      return nodes.reduce((count, node) => {
        if (node.type === 'file' && (node.status === 'complete' || node.status === 'ready')) {
          return count + 1;
        }
        return count + (node.children ? countCompleted(node.children) : 0);
      }, 0);
    };
    return countCompleted(fileTree);
  }, [fileTree]);

  return (
    <div className={`bg-white border rounded-lg ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">Project Files</h3>
          <div className="text-xs text-gray-500">
            {completedFiles}/{totalFiles} files
          </div>
        </div>
        <div className="mt-2">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${totalFiles > 0 ? (completedFiles / totalFiles) * 100 : 0}%` }}
            />
          </div>
        </div>
      </div>
      
      {/* File Tree */}
      <div className="p-2 max-h-96 overflow-y-auto">
        {fileTree.map(node => (
          <FileTreeNode
            key={node.id}
            node={{
              ...node,
              isOpen: expandedNodes.has(node.id)
            }}
            level={0}
            onFileSelect={onFileSelect}
            selectedFile={selectedFile}
            onToggle={handleToggle}
          />
        ))}
      </div>
      
      {/* Footer */}
      <div className="px-4 py-2 border-t bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {generatingFiles?.size || 0} files generating
          </span>
          <span>
            Click files to view content
          </span>
        </div>
      </div>
    </div>
  );
};

// Helper function to create a complete project file structure
export const createProjectFileStructure = (projectName: string, projectType: string = 'agency'): FileNode[] => {
  return [
    {
      id: 'root',
      name: projectName,
      type: 'folder',
      path: '/',
      status: 'complete',
      isOpen: true,
      children: [
        {
          id: 'bolt',
          name: '.bolt',
          type: 'folder',
          path: '/.bolt',
          status: 'complete',
          children: []
        },
        {
          id: 'src',
          name: 'src',
          type: 'folder',
          path: '/src',
          status: 'complete',
          isOpen: true,
          children: [
            {
              id: 'components',
              name: 'components',
              type: 'folder',
              path: '/src/components',
              status: 'complete',
              isOpen: true,
              children: [
                {
                  id: 'header-tsx',
                  name: 'Header.tsx',
                  type: 'file',
                  path: '/src/components/Header.tsx',
                  status: 'pending',
                  size: 2048
                },
                {
                  id: 'hero-tsx',
                  name: 'Hero.tsx',
                  type: 'file',
                  path: '/src/components/Hero.tsx',
                  status: 'pending',
                  size: 1536
                },
                {
                  id: 'services-tsx',
                  name: 'Services.tsx',
                  type: 'file',
                  path: '/src/components/Services.tsx',
                  status: 'pending',
                  size: 3072
                },
                {
                  id: 'footer-tsx',
                  name: 'Footer.tsx',
                  type: 'file',
                  path: '/src/components/Footer.tsx',
                  status: 'pending',
                  size: 1024
                }
              ]
            },
            {
              id: 'styles',
              name: 'styles',
              type: 'folder',
              path: '/src/styles',
              status: 'complete',
              children: [
                {
                  id: 'globals-css',
                  name: 'globals.css',
                  type: 'file',
                  path: '/src/styles/globals.css',
                  status: 'pending',
                  size: 512
                }
              ]
            },
            {
              id: 'app-tsx',
              name: 'App.tsx',
              type: 'file',
              path: '/src/App.tsx',
              status: 'pending',
              size: 1024
            },
            {
              id: 'main-tsx',
              name: 'main.tsx',
              type: 'file',
              path: '/src/main.tsx',
              status: 'pending',
              size: 256
            }
          ]
        },
        {
          id: 'public',
          name: 'public',
          type: 'folder',
          path: '/public',
          status: 'complete',
          children: [
            {
              id: 'favicon-ico',
              name: 'favicon.ico',
              type: 'file',
              path: '/public/favicon.ico',
              status: 'pending',
              size: 1024
            },
            {
              id: 'index-html',
              name: 'index.html',
              type: 'file',
              path: '/public/index.html',
              status: 'pending',
              size: 512
            }
          ]
        },
        {
          id: 'package-json',
          name: 'package.json',
          type: 'file',
          path: '/package.json',
          status: 'pending',
          size: 1024
        },
        {
          id: 'package-lock-json',
          name: 'package-lock.json',
          type: 'file',
          path: '/package-lock.json',
          status: 'pending',
          size: 50000
        },
        {
          id: 'tsconfig-json',
          name: 'tsconfig.json',
          type: 'file',
          path: '/tsconfig.json',
          status: 'pending',
          size: 512
        },
        {
          id: 'vite-config-ts',
          name: 'vite.config.ts',
          type: 'file',
          path: '/vite.config.ts',
          status: 'pending',
          size: 256
        },
        {
          id: 'tailwind-config-js',
          name: 'tailwind.config.js',
          type: 'file',
          path: '/tailwind.config.js',
          status: 'pending',
          size: 512
        },
        {
          id: 'postcss-config-js',
          name: 'postcss.config.js',
          type: 'file',
          path: '/postcss.config.js',
          status: 'pending',
          size: 128
        },
        {
          id: 'eslint-config-js',
          name: 'eslint.config.js',
          type: 'file',
          path: '/eslint.config.js',
          status: 'pending',
          size: 1024
        },
        {
          id: 'gitignore',
          name: '.gitignore',
          type: 'file',
          path: '/.gitignore',
          status: 'pending',
          size: 256
        },
        {
          id: 'readme-md',
          name: 'README.md',
          type: 'file',
          path: '/README.md',
          status: 'pending',
          size: 2048
        }
      ]
    }
  ];
};
