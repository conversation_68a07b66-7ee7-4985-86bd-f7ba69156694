import React, { useState } from 'react';
import { ProjectGenerator } from '../components/ProjectGenerator';
import { getAllTemplates, ProjectTemplate } from '../lib/projectTemplates';
import { Zap, Code, Palette, Rocket, Star, Clock, Users, Download } from 'lucide-react';

export const BoltDemo: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('modern-agency');
  const [projectName, setProjectName] = useState<string>('My Awesome Project');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const templates = getAllTemplates();

  const features = [
    {
      icon: <Zap className="w-6 h-6 text-yellow-500" />,
      title: 'Real-time Generation',
      description: 'Watch your project come to life with live progress tracking and smooth animations'
    },
    {
      icon: <Code className="w-6 h-6 text-blue-500" />,
      title: 'Complete Project Structure',
      description: 'Generate full project with all config files, dependencies, and documentation'
    },
    {
      icon: <Palette className="w-6 h-6 text-purple-500" />,
      title: 'Smooth Code Writing',
      description: 'Experience realistic code writing with typewriter effects and syntax highlighting'
    },
    {
      icon: <Rocket className="w-6 h-6 text-green-500" />,
      title: 'NPM Install Visualization',
      description: 'See package installation progress with detailed terminal output'
    }
  ];

  const stats = [
    { label: 'Projects Generated', value: '10,000+', icon: <Star className="w-5 h-5" /> },
    { label: 'Average Time', value: '45s', icon: <Clock className="w-5 h-5" /> },
    { label: 'Happy Developers', value: '5,000+', icon: <Users className="w-5 h-5" /> },
    { label: 'Files Created', value: '500K+', icon: <Download className="w-5 h-5" /> }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold">
                Enhanced Bolt Experience
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                Professional project generation with real-time progress, smooth animations, 
                and complete file structure visualization
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-2 text-blue-200">
                    {stat.icon}
                  </div>
                  <div className="text-2xl md:text-3xl font-bold">{stat.value}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Professional Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the next generation of AI-powered development tools
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow">
                <div className="mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Project Configuration */}
      <div className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Configure Your Project
              </h2>
              <p className="text-gray-600">
                Choose a template and customize your project settings
              </p>
            </div>

            <div className="space-y-6">
              {/* Project Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Name
                </label>
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your project name"
                />
              </div>

              {/* Template Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Choose Template
                </label>
                <div className="grid gap-4">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`
                        border rounded-lg p-4 cursor-pointer transition-all
                        ${selectedTemplate === template.id 
                          ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500' 
                          : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">
                            {template.name}
                          </h3>
                          <p className="text-gray-600 text-sm mb-3">
                            {template.description}
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {template.technologies.slice(0, 4).map((tech) => (
                              <span
                                key={tech}
                                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                              >
                                {tech}
                              </span>
                            ))}
                            {template.technologies.length > 4 && (
                              <span className="text-gray-500 text-xs">
                                +{template.technologies.length - 4} more
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="text-right ml-4">
                          <div className="text-sm text-gray-500">
                            ~{template.estimatedTime}s
                          </div>
                          <div className="text-xs text-gray-400">
                            {template.steps.length} steps
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Project Generator */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <ProjectGenerator
            templateId={selectedTemplate}
            projectName={projectName}
            onComplete={() => {
              setIsGenerating(false);
              console.log('Project generation complete!');
            }}
          />
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Build Something Amazing?
            </h3>
            <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
              Experience the future of AI-powered development with our enhanced Bolt platform. 
              Generate complete projects with professional-grade tooling and smooth animations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Start Building
              </button>
              <button className="border border-gray-600 text-gray-300 px-8 py-3 rounded-lg hover:border-gray-500 transition-colors">
                View Documentation
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};
