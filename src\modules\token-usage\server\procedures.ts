import { getProjectTokenUsage, getMessageTokenUsage } from "@/lib/token-tracker";
import { createTRPCRouter, protectedProcedure } from "@/trpc/init";
import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { prisma } from "@/lib/db";

export const tokenUsageRouter = createTRPCRouter({
  // Get token usage for a specific project
  getProjectUsage: protectedProcedure
    .input(z.object({
      projectId: z.string().min(1, { message: "Project ID is required" }),
    }))
    .query(async ({ input, ctx }) => {
      // Verify project ownership
      const project = await prisma.project.findUnique({
        where: {
          id: input.projectId,
          userId: ctx.auth.userId,
        },
      });

      if (!project) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Project not found" });
      }

      try {
        const result = await getProjectTokenUsage(input.projectId);
        return result;
      } catch (error) {
        throw new TRPCError({ 
          code: "INTERNAL_SERVER_ERROR", 
          message: "Failed to retrieve token usage" 
        });
      }
    }),

  // Get token usage for a specific message
  getMessageUsage: protectedProcedure
    .input(z.object({
      messageId: z.string().min(1, { message: "Message ID is required" }),
    }))
    .query(async ({ input, ctx }) => {
      // Verify message ownership through project
      const message = await prisma.message.findUnique({
        where: { id: input.messageId },
        include: { project: true },
      });

      if (!message || message.project.userId !== ctx.auth.userId) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Message not found" });
      }

      try {
        const usages = await getMessageTokenUsage(input.messageId);
        return usages;
      } catch (error) {
        throw new TRPCError({ 
          code: "INTERNAL_SERVER_ERROR", 
          message: "Failed to retrieve message token usage" 
        });
      }
    }),

  // Get overall token usage statistics for the user
  getUserStats: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const userProjects = await prisma.project.findMany({
          where: { userId: ctx.auth.userId },
          select: { id: true },
        });

        const projectIds = userProjects.map(p => p.id);

        const totalUsage = await prisma.tokenUsage.findMany({
          where: {
            projectId: { in: projectIds },
          },
        });

        const stats = totalUsage.reduce(
          (acc, usage) => ({
            totalInputTokens: acc.totalInputTokens + usage.inputTokens,
            totalOutputTokens: acc.totalOutputTokens + usage.outputTokens,
            totalTokens: acc.totalTokens + usage.totalTokens,
            totalCost: acc.totalCost + (usage.estimatedCost || 0),
            requestCount: acc.requestCount + 1,
          }),
          {
            totalInputTokens: 0,
            totalOutputTokens: 0,
            totalTokens: 0,
            totalCost: 0,
            requestCount: 0,
          }
        );

        // Get usage by model
        const usageByModel = totalUsage.reduce((acc, usage) => {
          if (!acc[usage.modelName]) {
            acc[usage.modelName] = {
              inputTokens: 0,
              outputTokens: 0,
              totalTokens: 0,
              cost: 0,
              requests: 0,
            };
          }
          acc[usage.modelName].inputTokens += usage.inputTokens;
          acc[usage.modelName].outputTokens += usage.outputTokens;
          acc[usage.modelName].totalTokens += usage.totalTokens;
          acc[usage.modelName].cost += usage.estimatedCost || 0;
          acc[usage.modelName].requests += 1;
          return acc;
        }, {} as Record<string, any>);

        // Get usage by agent
        const usageByAgent = totalUsage.reduce((acc, usage) => {
          if (!acc[usage.agentName]) {
            acc[usage.agentName] = {
              inputTokens: 0,
              outputTokens: 0,
              totalTokens: 0,
              cost: 0,
              requests: 0,
            };
          }
          acc[usage.agentName].inputTokens += usage.inputTokens;
          acc[usage.agentName].outputTokens += usage.outputTokens;
          acc[usage.agentName].totalTokens += usage.totalTokens;
          acc[usage.agentName].cost += usage.estimatedCost || 0;
          acc[usage.agentName].requests += 1;
          return acc;
        }, {} as Record<string, any>);

        return {
          overall: stats,
          byModel: usageByModel,
          byAgent: usageByAgent,
          recentUsage: totalUsage
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(0, 10),
        };
      } catch (error) {
        throw new TRPCError({ 
          code: "INTERNAL_SERVER_ERROR", 
          message: "Failed to retrieve user statistics" 
        });
      }
    }),

  // Get token usage trends over time
  getUsageTrends: protectedProcedure
    .input(z.object({
      projectId: z.string().optional(),
      days: z.number().min(1).max(90).default(30),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - input.days);

        let whereClause: any = {
          createdAt: { gte: startDate },
        };

        if (input.projectId) {
          // Verify project ownership
          const project = await prisma.project.findUnique({
            where: {
              id: input.projectId,
              userId: ctx.auth.userId,
            },
          });

          if (!project) {
            throw new TRPCError({ code: "NOT_FOUND", message: "Project not found" });
          }

          whereClause.projectId = input.projectId;
        } else {
          // Get all user projects
          const userProjects = await prisma.project.findMany({
            where: { userId: ctx.auth.userId },
            select: { id: true },
          });
          whereClause.projectId = { in: userProjects.map(p => p.id) };
        }

        const usageData = await prisma.tokenUsage.findMany({
          where: whereClause,
          orderBy: { createdAt: 'asc' },
        });

        // Group by day
        const dailyUsage = usageData.reduce((acc, usage) => {
          const date = usage.createdAt.toISOString().split('T')[0];
          if (!acc[date]) {
            acc[date] = {
              date,
              inputTokens: 0,
              outputTokens: 0,
              totalTokens: 0,
              cost: 0,
              requests: 0,
            };
          }
          acc[date].inputTokens += usage.inputTokens;
          acc[date].outputTokens += usage.outputTokens;
          acc[date].totalTokens += usage.totalTokens;
          acc[date].cost += usage.estimatedCost || 0;
          acc[date].requests += 1;
          return acc;
        }, {} as Record<string, any>);

        return Object.values(dailyUsage);
      } catch (error) {
        throw new TRPCError({ 
          code: "INTERNAL_SERVER_ERROR", 
          message: "Failed to retrieve usage trends" 
        });
      }
    }),
});
