"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  GenerationProgress,
  useGenerationProgress,
  type GenerationStep
} from "./generation-progress";
import { EnhancedFileTree } from "./enhanced-file-tree";
import { CodeStreamWriter } from "./code-stream-writer";
import { NPMInstallVisualizer } from "./npm-install-visualizer";

interface ProjectData {
  files: { [path: string]: string };
  projectType: string;
  title: string;
}

interface BoltGenerationSuiteProps {
  projectData: ProjectData;
  onGenerationComplete?: () => void;
}

type GenerationPhase = 'setup' | 'dependencies' | 'files' | 'complete';

export function BoltGenerationSuite({ 
  projectData, 
  onGenerationComplete 
}: BoltGenerationSuiteProps) {
  const [currentPhase, setCurrentPhase] = useState<GenerationPhase>('setup');
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStarted, setGenerationStarted] = useState(false);
  
  // Use the generation progress hook
  const {
    steps,
    currentStep,
    progress,
    isComplete,
    startGeneration,
    pauseGeneration,
    resetGeneration
  } = useGenerationProgress(projectData);

  // Auto-select first file when files are available
  useEffect(() => {
    if (projectData.files && Object.keys(projectData.files).length > 0 && !selectedFile) {
      const firstFile = Object.keys(projectData.files)[0];
      setSelectedFile(firstFile);
    }
  }, [projectData.files, selectedFile]);

  // Update phase based on progress
  useEffect(() => {
    if (progress === 0) {
      setCurrentPhase('setup');
    } else if (progress < 30) {
      setCurrentPhase('dependencies');
    } else if (progress < 100) {
      setCurrentPhase('files');
    } else {
      setCurrentPhase('complete');
      onGenerationComplete?.();
    }
  }, [progress, onGenerationComplete]);

  const handleStartGeneration = () => {
    setIsGenerating(true);
    setGenerationStarted(true);
    startGeneration();
  };

  const handlePauseGeneration = () => {
    setIsGenerating(false);
    pauseGeneration();
  };

  const handleResetGeneration = () => {
    setIsGenerating(false);
    setGenerationStarted(false);
    setCurrentPhase('setup');
    setSelectedFile(null);
    resetGeneration();
  };

  const fileEntries = Object.entries(projectData.files || {});
  const totalFiles = fileEntries.length;
  const completedFiles = steps.filter(step => 
    step.status === 'completed' && step.type === 'file'
  ).length;

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-card">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-xl font-semibold">{projectData.title}</h1>
            <p className="text-sm text-muted-foreground">
              {projectData.projectType} • {totalFiles} files
            </p>
          </div>
          
          {generationStarted && (
            <div className="flex items-center gap-2">
              <Badge variant={currentPhase === 'complete' ? 'default' : 'secondary'}>
                {currentPhase === 'setup' && 'Setting up...'}
                {currentPhase === 'dependencies' && 'Installing dependencies...'}
                {currentPhase === 'files' && `Generating files... (${completedFiles}/${totalFiles})`}
                {currentPhase === 'complete' && 'Complete!'}
              </Badge>
              
              <div className="flex items-center gap-2">
                <Progress value={progress} className="w-24 h-2" />
                <span className="text-xs font-mono">{Math.round(progress)}%</span>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {!generationStarted && (
            <Button onClick={handleStartGeneration} size="sm">
              Start Generation
            </Button>
          )}
          
          {generationStarted && !isComplete && (
            <>
              <Button
                onClick={isGenerating ? handlePauseGeneration : handleStartGeneration}
                variant={isGenerating ? "destructive" : "default"}
                size="sm"
              >
                {isGenerating ? 'Pause' : 'Resume'}
              </Button>
              
              <Button onClick={handleResetGeneration} variant="outline" size="sm">
                Reset
              </Button>
            </>
          )}
          
          {isComplete && (
            <Button onClick={handleResetGeneration} variant="outline" size="sm">
              New Generation
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Progress & Files */}
        <div className="w-80 border-r bg-card flex flex-col">
          <Tabs defaultValue="progress" className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 m-2">
              <TabsTrigger value="progress">Progress</TabsTrigger>
              <TabsTrigger value="files">Files</TabsTrigger>
              <TabsTrigger value="deps">Deps</TabsTrigger>
            </TabsList>
            
            <TabsContent value="progress" className="flex-1 m-0 p-2">
              <GenerationProgress
                steps={steps}
                currentStep={currentStep}
                onStepClick={(step) => {
                  if (step.type === 'file' && step.fileName && projectData.files[step.fileName]) {
                    setSelectedFile(step.fileName);
                  }
                }}
              />
            </TabsContent>
            
            <TabsContent value="files" className="flex-1 m-0 p-2">
              <EnhancedFileTree
                files={projectData.files || {}}
                selectedFile={selectedFile}
                onFileSelect={setSelectedFile}
                generationSteps={steps}
              />
            </TabsContent>
            
            <TabsContent value="deps" className="flex-1 m-0 p-2">
              <NPMInstallVisualizer
                projectType={projectData.projectType}
                isInstalling={currentPhase === 'dependencies'}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Content - Code & Preview */}
        <div className="flex-1 flex flex-col">
          <Tabs defaultValue="code" className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-2 m-2">
              <TabsTrigger value="code">Code</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>
            
            <TabsContent value="code" className="flex-1 m-0 overflow-hidden">
              {selectedFile && projectData.files[selectedFile] ? (
                <CodeStreamWriter
                  fileName={selectedFile}
                  content={projectData.files[selectedFile]}
                  isStreaming={
                    isGenerating && 
                    steps.find(step => step.fileName === selectedFile)?.status === 'in-progress'
                  }
                  language={getLanguageFromFile(selectedFile)}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📝</div>
                    <h3 className="text-lg font-medium mb-2">No file selected</h3>
                    <p className="text-sm">Select a file from the sidebar to view its content</p>
                  </div>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="preview" className="flex-1 m-0">
              <div className="h-full bg-background border rounded-lg m-2 flex items-center justify-center">
                {isComplete ? (
                  <div className="text-center">
                    <div className="text-4xl mb-4">🚀</div>
                    <h3 className="text-lg font-medium mb-2">Preview Ready</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Your project is complete and ready for preview
                    </p>
                    <Button size="sm">Open Preview</Button>
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    <div className="text-4xl mb-4">⏳</div>
                    <h3 className="text-lg font-medium mb-2">Preview Pending</h3>
                    <p className="text-sm">Preview will be available when generation is complete</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-3 border-t bg-card text-sm text-muted-foreground">
        <div className="flex items-center gap-6">
          <div>
            Phase: <span className="font-medium capitalize">{currentPhase}</span>
          </div>
          <div>
            Files: <span className="font-medium">{completedFiles}/{totalFiles}</span>
          </div>
          <div>
            Progress: <span className="font-medium">{Math.round(progress)}%</span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {currentStep && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>{currentStep.name}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper function to determine language from file extension
function getLanguageFromFile(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'tsx':
    case 'jsx':
      return 'tsx';
    case 'ts':
      return 'typescript';
    case 'js':
      return 'javascript';
    case 'css':
      return 'css';
    case 'json':
      return 'json';
    case 'md':
      return 'markdown';
    case 'html':
      return 'html';
    default:
      return 'text';
  }
}