"use client";

import React, { useState, useEffect } from 'react';
import { 
  ChevronRightIcon, 
  ChevronDownIcon, 
  FileIcon, 
  FolderIcon,
  ImageIcon,
  SettingsIcon,
  PackageIcon
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface FileTreeNode {
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileTreeNode[];
  content?: string;
  isNew?: boolean;
  status?: 'pending' | 'generating' | 'completed';
  fileType?: 'component' | 'config' | 'package' | 'style' | 'image' | 'other';
}

interface EnhancedFileTreeProps {
  files: FileTreeNode[];
  onFileSelect: (file: FileTreeNode) => void;
  selectedFile?: string;
  className?: string;
  showGenerationStatus?: boolean;
}

const getFileIcon = (fileName: string, fileType?: string) => {
  if (fileType === 'package' || fileName === 'package.json') {
    return PackageIcon;
  }
  if (fileType === 'config' || fileName.includes('config') || fileName.includes('.json')) {
    return SettingsIcon;
  }
  if (fileType === 'image' || /\.(jpg|jpeg|png|gif|svg|webp)$/i.test(fileName)) {
    return ImageIcon;
  }
  return FileIcon;
};

const getFileTypeColor = (fileName: string, fileType?: string) => {
  if (fileType === 'package' || fileName === 'package.json') {
    return 'text-green-400';
  }
  if (fileType === 'config' || fileName.includes('config')) {
    return 'text-yellow-400';
  }
  if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
    return 'text-blue-400';
  }
  if (fileName.endsWith('.ts') || fileName.endsWith('.js')) {
    return 'text-yellow-500';
  }
  if (fileName.endsWith('.css') || fileName.endsWith('.scss')) {
    return 'text-pink-400';
  }
  if (fileType === 'image' || /\.(jpg|jpeg|png|gif|svg|webp)$/i.test(fileName)) {
    return 'text-purple-400';
  }
  return 'text-gray-400';
};

export const EnhancedFileTree: React.FC<EnhancedFileTreeProps> = ({ 
  files, 
  onFileSelect, 
  selectedFile,
  className,
  showGenerationStatus = false
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['src', 'components']));
  const [animatingFiles, setAnimatingFiles] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Track new files for animation
    const newFiles = new Set<string>();
    
    const collectNewFiles = (nodes: FileTreeNode[], path = '') => {
      nodes.forEach(node => {
        const fullPath = path ? `${path}/${node.name}` : node.name;
        if (node.isNew) {
          newFiles.add(fullPath);
        }
        if (node.children) {
          collectNewFiles(node.children, fullPath);
        }
      });
    };
    
    collectNewFiles(files);
    
    if (newFiles.size > 0) {
      setAnimatingFiles(newFiles);
      
      // Auto-expand folders containing new files
      const foldersToExpand = new Set<string>();
      newFiles.forEach(filePath => {
        const pathParts = filePath.split('/');
        for (let i = 0; i < pathParts.length - 1; i++) {
          const folderPath = pathParts.slice(0, i + 1).join('/');
          foldersToExpand.add(folderPath);
        }
      });
      
      if (foldersToExpand.size > 0) {
        setExpandedFolders(prev => new Set([...prev, ...foldersToExpand]));
      }
      
      const timer = setTimeout(() => {
        setAnimatingFiles(new Set());
      }, 3000); // Increased animation time for better visibility
      return () => clearTimeout(timer);
    }
  }, [files]);

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const renderNode = (node: FileTreeNode, parentPath: string = '', depth: number = 0) => {
    const fullPath = parentPath ? `${parentPath}/${node.name}` : node.name;
    const isExpanded = expandedFolders.has(fullPath);
    const isSelected = selectedFile === fullPath;
    const isAnimating = animatingFiles.has(fullPath);
    const FileIconComponent = getFileIcon(node.name, node.fileType);
    const fileColor = getFileTypeColor(node.name, node.fileType);

    return (
      <div 
        key={fullPath} 
        className={cn(
          "transition-all duration-300",
          isAnimating && "animate-pulse bg-green-500/10 rounded-md"
        )}
      >
        <div
          className={cn(
            "flex items-center gap-2 py-1 px-2 rounded-md cursor-pointer transition-all duration-300",
            "hover:bg-gray-800/50 hover:scale-[1.02]",
            isSelected && "bg-blue-500/20 border border-blue-500/30 shadow-lg shadow-blue-500/10",
            isAnimating && "bg-green-500/20 border border-green-500/30 shadow-lg shadow-green-500/10 animate-pulse"
          )}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(fullPath);
            } else {
              onFileSelect(node);
            }
          }}
        >
          {/* Folder toggle icon */}
          {node.type === 'folder' && (
            <div className="flex items-center">
              {isExpanded ? (
                <ChevronDownIcon className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
              )}
            </div>
          )}
          
          {/* File/Folder icon */}
          <div className={cn("flex items-center", fileColor)}>
            {node.type === 'folder' ? (
              <FolderIcon className={cn("w-4 h-4", isExpanded ? "text-blue-400" : "text-gray-500")} />
            ) : (
              <FileIconComponent className="w-4 h-4" />
            )}
          </div>

          {/* Name */}
          <span className={cn(
            "text-sm flex-1 truncate",
            isSelected ? "text-white font-medium" : "text-gray-300",
            isAnimating && "text-green-400"
          )}>
            {node.name}
          </span>

          {/* Status badges */}
          <div className="flex items-center gap-1">
            {isAnimating && (
              <Badge variant="outline" className="text-xs px-1 py-0 border-green-500/50 text-green-400 animate-bounce">
                NEW
              </Badge>
            )}
            
            {node.status === 'generating' && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse" />
                <span className="text-xs text-blue-400 animate-pulse">Writing...</span>
              </div>
            )}
            
            {node.status === 'completed' && !isAnimating && (
              <div className="w-2 h-2 rounded-full bg-green-400 animate-ping" />
            )}
            
            {node.fileType === 'config' && (
              <Badge variant="outline" className="text-xs px-1 py-0 border-yellow-500/50 text-yellow-400 bg-yellow-500/5">
                CONFIG
              </Badge>
            )}
            
            {node.fileType === 'package' && (
              <Badge variant="outline" className="text-xs px-1 py-0 border-green-500/50 text-green-400 bg-green-500/5">
                PKG
              </Badge>
            )}
            
            {node.fileType === 'image' && (
              <Badge variant="outline" className="text-xs px-1 py-0 border-purple-500/50 text-purple-400 bg-purple-500/5">
                IMG
              </Badge>
            )}
          </div>
        </div>
        
        {/* Children */}
        {node.type === 'folder' && isExpanded && node.children && (
          <div className={cn(
            "transition-all duration-200 ease-in-out",
            isExpanded ? "opacity-100" : "opacity-0"
          )}>
            {node.children.map(child => renderNode(child, fullPath, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  const totalFiles = files.reduce((count, node) => {
    const countFiles = (n: FileTreeNode): number => {
      let count = n.type === 'file' ? 1 : 0;
      if (n.children) {
        count += n.children.reduce((sum, child) => sum + countFiles(child), 0);
      }
      return count;
    };
    return count + countFiles(node);
  }, 0);

  const completedFiles = files.reduce((count, node) => {
    const countCompleted = (n: FileTreeNode): number => {
      let count = (n.type === 'file' && n.status === 'completed') ? 1 : 0;
      if (n.children) {
        count += n.children.reduce((sum, child) => sum + countCompleted(child), 0);
      }
      return count;
    };
    return count + countCompleted(node);
  }, 0);

  // Get currently generating file
  const generatingFile = files.find(file => {
    const checkGenerating = (node: FileTreeNode): boolean => {
      if (node.status === 'generating') return true;
      if (node.children) {
        return node.children.some(checkGenerating);
      }
      return false;
    };
    return checkGenerating(file);
  });

  const findGeneratingFile = (nodes: FileTreeNode[]): FileTreeNode | null => {
    for (const node of nodes) {
      if (node.status === 'generating') return node;
      if (node.children) {
        const found = findGeneratingFile(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  const currentlyGenerating = findGeneratingFile(files);

  return (
    <div className={cn("bg-gray-950 text-white border-r border-gray-800", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-white">Project Files</h3>
          {totalFiles > 0 && (
            <Badge variant="outline" className="text-xs">
              {completedFiles}/{totalFiles}
            </Badge>
          )}
        </div>
        
        {/* Real-time generation status */}
        {showGenerationStatus && currentlyGenerating && (
          <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              <span className="text-sm text-blue-400 font-medium">Generating...</span>
            </div>
            <div className="mt-1">
              <span className="text-xs text-gray-300">{currentlyGenerating.name}</span>
            </div>
            <div className="mt-2 h-1 bg-gray-800 rounded-full overflow-hidden">
              <div className="h-full bg-blue-500 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          </div>
        )}
        
        {/* Show queue of upcoming files */}
        {showGenerationStatus && files.filter(f => f.status === 'pending').length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-500 mb-2">Upcoming files:</div>
            <div className="space-y-1">
              {files.filter(f => f.status === 'pending').slice(0, 3).map((file, index) => (
                <div key={file.path} className="flex items-center gap-2 text-xs text-gray-400">
                  <div className="w-1 h-1 bg-gray-600 rounded-full" />
                  <span>{file.name}</span>
                </div>
              ))}
              {files.filter(f => f.status === 'pending').length > 3 && (
                <div className="text-xs text-gray-500">
                  +{files.filter(f => f.status === 'pending').length - 3} more files...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      
      {/* File Tree */}
      <div className="overflow-y-auto max-h-[calc(100vh-200px)] p-2">
        {files.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FolderIcon className="w-12 h-12 text-gray-600 mb-3" />
            <p className="text-gray-500 text-sm">
              Files will appear here as they're generated
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {files.map(node => renderNode(node))}
          </div>
        )}
      </div>
      
      {/* Footer */}
      {totalFiles > 0 && (
        <div className="p-3 border-t border-gray-800 text-xs text-gray-500">
          <div className="flex justify-between">
            <span>{totalFiles} files</span>
            <span>{animatingFiles.size} generating</span>
          </div>
        </div>
      )}
    </div>
  );
};