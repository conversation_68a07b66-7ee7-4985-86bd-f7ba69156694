import { createApi } from 'unsplash-js';
import { getUnsplashPattern, detectProjectType } from './unsplash-patterns';

// Types for our Unsplash integration
export interface UnsplashImage {
  id: string;
  url: string;
  alt: string;
  width: number;
  height: number;
  attribution: {
    photographer: string;
    photographerUrl: string;
    unsplashUrl: string;
  };
}

export interface UnsplashSearchResult {
  images: UnsplashImage[];
  total: number;
  totalPages: number;
}

class UnsplashService {
  private api: ReturnType<typeof createApi> | null = null;
  private rateLimitRemaining = 50; // Default rate limit
  private rateLimitResetTime = Date.now();

  constructor() {
    const accessKey = process.env.UNSPLASH_ACCESS_KEY || process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY;
    if (accessKey) {
      this.api = createApi({
        accessKey,
      });
    }
  }

  /**
   * Check if Unsplash is available
   */
  isAvailable(): boolean {
    return this.api !== null;
  }

  /**
   * Get rate limit info
   */
  getRateLimitInfo() {
    return {
      remaining: this.rateLimitRemaining,
      resetTime: this.rateLimitResetTime,
    };
  }

  /**
   * Search for images based on query with smart categorization
   */
  async searchImages(query: string, count: number = 6): Promise<UnsplashImage[]> {
    if (!this.api) {
      console.warn('Unsplash API not configured, using placeholder images');
      return this.createPlaceholderImages(count);
    }

    if (this.rateLimitRemaining <= 0 && Date.now() < this.rateLimitResetTime) {
      console.warn('Rate limit exceeded, using placeholder images');
      return this.createPlaceholderImages(count);
    }

    try {
      // Enhance query with relevant keywords for better results
      const enhancedQuery = this.enhanceSearchQuery(query);
      
      const result = await this.api.search.getPhotos({
        query: enhancedQuery,
        page: 1,
        perPage: Math.min(count, 30),
        orientation: 'landscape',
        orderBy: 'relevant',
      });

      // Update rate limit info from headers
      this.updateRateLimitInfo(result.response);

      if (!result.response || result.response.results.length === 0) {
        // Fallback to generic query if specific search fails
        return this.searchFallbackImages(count);
      }

      return result.response.results.map(photo => ({
        id: photo.id,
        url: photo.urls.regular,
        alt: photo.alt_description || photo.description || `Photo by ${photo.user.name}`,
        width: photo.width,
        height: photo.height,
        attribution: {
          photographer: photo.user.name,
          photographerUrl: photo.user.links.html,
          unsplashUrl: photo.links.html,
        },
      }));
    } catch (error) {
      console.error('Unsplash search error:', error);
      // Return placeholders instead of throwing
      return this.createPlaceholderImages(count);
    }
  }

  /**
   * Get curated images for specific project types
   */
  async getCuratedImages(projectType: string, context: string = 'primary', count: number = 6): Promise<UnsplashImage[]> {
    const queries = getUnsplashPattern(projectType, context);
    
    // Try each query until we get results
    for (const query of queries) {
      try {
        const images = await this.searchImages(query, count);
        if (images.length > 0) {
          return images;
        }
      } catch (error) {
        console.warn(`Failed to get images for query: ${query}`, error);
      }
    }

    // Final fallback
    return this.searchFallbackImages(count);
  }

  /**
   * Get images based on project prompt
   */
  async getProjectImages(prompt: string, context: string = 'primary', count: number = 6): Promise<UnsplashImage[]> {
    const projectType = detectProjectType(prompt);
    return this.getCuratedImages(projectType, context, count);
  }

  /**
   * Generate HTML attribution for images
   */
  generateAttributionHtml(images: UnsplashImage[]): string {
    if (images.length === 0) return '';

    const attributions = images.map(img => 
      `Photo by <a href="${img.attribution.photographerUrl}?utm_source=vibe&utm_medium=referral" target="_blank" rel="noopener">${img.attribution.photographer}</a> on <a href="https://unsplash.com/?utm_source=vibe&utm_medium=referral" target="_blank" rel="noopener">Unsplash</a>`
    );

    return `<div class="image-attributions">${attributions.join('<br>')}</div>`;
  }

  /**
   * Enhance search query with relevant keywords
   */
  private enhanceSearchQuery(query: string): string {
    const lowerQuery = query.toLowerCase();
    
    // Add relevant keywords based on common project types
    if (lowerQuery.includes('ecommerce') || lowerQuery.includes('shop')) {
      return `${query} shopping retail store`;
    }
    if (lowerQuery.includes('blog') || lowerQuery.includes('article')) {
      return `${query} writing content blog`;
    }
    if (lowerQuery.includes('portfolio') || lowerQuery.includes('personal')) {
      return `${query} creative professional work`;
    }
    if (lowerQuery.includes('dashboard') || lowerQuery.includes('admin')) {
      return `${query} analytics data charts`;
    }
    if (lowerQuery.includes('landing') || lowerQuery.includes('marketing')) {
      return `${query} business marketing professional`;
    }
    if (lowerQuery.includes('restaurant') || lowerQuery.includes('food')) {
      return `${query} food restaurant dining`;
    }
    if (lowerQuery.includes('travel') || lowerQuery.includes('hotel')) {
      return `${query} travel vacation destination`;
    }
    
    return query;
  }

  /**
   * Get search queries based on project type
   */
  private getQueriesForProjectType(projectType: string): string[] {
    const typeQueries: Record<string, string[]> = {
      ecommerce: ['shopping', 'retail store', 'products', 'commerce'],
      blog: ['writing', 'content', 'articles', 'blog'],
      portfolio: ['creative work', 'professional', 'portfolio', 'design'],
      dashboard: ['analytics', 'data visualization', 'charts', 'dashboard'],
      landing: ['business', 'marketing', 'professional', 'startup'],
      restaurant: ['food', 'restaurant', 'dining', 'culinary'],
      travel: ['travel', 'vacation', 'destination', 'adventure'],
      fitness: ['fitness', 'exercise', 'health', 'workout'],
      education: ['education', 'learning', 'books', 'study'],
      finance: ['finance', 'money', 'business', 'banking'],
      technology: ['technology', 'computer', 'innovation', 'digital'],
      healthcare: ['healthcare', 'medical', 'doctor', 'hospital'],
      real_estate: ['real estate', 'property', 'house', 'home'],
      photography: ['photography', 'camera', 'art', 'creative'],
      music: ['music', 'sound', 'audio', 'performance'],
    };

    const lowerType = projectType.toLowerCase();
    
    // Try exact match first
    if (typeQueries[lowerType]) {
      return typeQueries[lowerType];
    }

    // Try partial matches
    for (const [key, queries] of Object.entries(typeQueries)) {
      if (lowerType.includes(key) || key.includes(lowerType)) {
        return queries;
      }
    }

    // Fallback to generic business images
    return ['business', 'professional', 'modern', 'clean'];
  }

  /**
   * Fallback search for generic high-quality images
   */
  private async searchFallbackImages(count: number): Promise<UnsplashImage[]> {
    if (!this.api) {
      return this.createPlaceholderImages(count);
    }

    const fallbackQueries = [
      'modern minimalist',
      'clean professional', 
      'abstract geometric',
      'nature landscape',
      'business professional',
    ];

    for (const query of fallbackQueries) {
      try {
        const result = await this.api.search.getPhotos({
          query,
          page: 1,
          perPage: Math.min(count, 30),
          orientation: 'landscape',
          orderBy: 'relevant',
        });

        if (result.response && result.response.results.length > 0) {
          return result.response.results.map(photo => ({
            id: photo.id,
            url: photo.urls.regular,
            alt: photo.alt_description || photo.description || `Photo by ${photo.user.name}`,
            width: photo.width,
            height: photo.height,
            attribution: {
              photographer: photo.user.name,
              photographerUrl: photo.user.links.html,
              unsplashUrl: photo.links.html,
            },
          }));
        }
      } catch (error) {
        console.warn(`Fallback query failed: ${query}`, error);
      }
    }

    // If all fails, return placeholder images
    return this.createPlaceholderImages(count);
  }

  /**
   * Create placeholder images when Unsplash is unavailable
   */
  private createPlaceholderImages(count: number): UnsplashImage[] {
    const placeholders = [];
    const colors = ['3B82F6', '10B981', 'F59E0B', 'EF4444', '8B5CF6', 'F97316'];
    
    for (let i = 0; i < count; i++) {
      const color = colors[i % colors.length];
      placeholders.push({
        id: `placeholder-${i}`,
        url: `https://via.placeholder.com/800x600/${color}/FFFFFF?text=Image+${i + 1}`,
        alt: `Placeholder image ${i + 1}`,
        width: 800,
        height: 600,
        attribution: {
          photographer: 'Placeholder',
          photographerUrl: '#',
          unsplashUrl: '#',
        },
      });
    }
    
    return placeholders;
  }

  /**
   * Update rate limit information from response headers
   */
  private updateRateLimitInfo(response: any) {
    if (response && response.headers) {
      const remaining = response.headers.get('x-ratelimit-remaining');
      const resetTime = response.headers.get('x-ratelimit-reset');
      
      if (remaining) {
        this.rateLimitRemaining = parseInt(remaining, 10);
      }
      
      if (resetTime) {
        this.rateLimitResetTime = parseInt(resetTime, 10) * 1000; // Convert to milliseconds
      }
    }
  }
}

// Export singleton instance
export const unsplashService = new UnsplashService();