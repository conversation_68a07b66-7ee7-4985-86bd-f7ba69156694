import { createApi } from 'unsplash-js';

// Unsplash API configuration
const UNSPLASH_ACCESS_KEY = '*******************************************';
const UNSPLASH_SECRET_KEY = '*******************************************';

// Initialize Unsplash API client
const unsplash = createApi({
  accessKey: UNSPLASH_ACCESS_KEY,
});

// Types for our Unsplash integration
export interface UnsplashImage {
  id: string;
  urls: {
    raw: string;
    full: string;
    regular: string;
    small: string;
    thumb: string;
  };
  alt_description: string | null;
  description: string | null;
  user: {
    name: string;
    username: string;
    links: {
      html: string;
    };
  };
  links: {
    html: string;
    download_location: string;
  };
  width: number;
  height: number;
}

export interface ImageSearchOptions {
  query?: string;
  category?: string;
  orientation?: 'landscape' | 'portrait' | 'squarish';
  size?: 'small' | 'regular' | 'full';
  color?: 'black_and_white' | 'black' | 'white' | 'yellow' | 'orange' | 'red' | 'purple' | 'magenta' | 'green' | 'teal' | 'blue';
  per_page?: number;
  page?: number;
}

export interface CachedImage extends UnsplashImage {
  cached_at: number;
  context?: string;
}

// In-memory cache for images (in production, use Redis or similar)
const imageCache = new Map<string, CachedImage>();
const CACHE_DURATION = 1000 * 60 * 60; // 1 hour

/**
 * Core Unsplash service class
 */
export class UnsplashService {
  private static instance: UnsplashService;
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT = 50; // requests per hour for demo
  private readonly RATE_WINDOW = 1000 * 60 * 60; // 1 hour

  private constructor() {}

  static getInstance(): UnsplashService {
    if (!UnsplashService.instance) {
      UnsplashService.instance = new UnsplashService();
    }
    return UnsplashService.instance;
  }

  /**
   * Check if we're within rate limits
   */
  private checkRateLimit(): boolean {
    const now = Date.now();
    if (now - this.lastRequestTime > this.RATE_WINDOW) {
      this.requestCount = 0;
      this.lastRequestTime = now;
    }
    return this.requestCount < this.RATE_LIMIT;
  }

  /**
   * Generate cache key for image requests
   */
  private getCacheKey(query: string, options: ImageSearchOptions = {}): string {
    const key = `${query}-${options.orientation || 'any'}-${options.size || 'regular'}-${options.color || 'any'}`;
    return key.toLowerCase().replace(/\s+/g, '-');
  }

  /**
   * Get cached image if available and not expired
   */
  private getCachedImage(cacheKey: string): CachedImage | null {
    const cached = imageCache.get(cacheKey);
    if (cached && Date.now() - cached.cached_at < CACHE_DURATION) {
      return cached;
    }
    if (cached) {
      imageCache.delete(cacheKey);
    }
    return null;
  }

  /**
   * Cache an image with metadata
   */
  private cacheImage(cacheKey: string, image: UnsplashImage, context?: string): CachedImage {
    const cachedImage: CachedImage = {
      ...image,
      cached_at: Date.now(),
      context,
    };
    imageCache.set(cacheKey, cachedImage);
    return cachedImage;
  }

  /**
   * Search for images by query with intelligent fallbacks
   */
  async searchImages(query: string, options: ImageSearchOptions = {}): Promise<UnsplashImage[]> {
    const cacheKey = this.getCacheKey(query, options);
    const cached = this.getCachedImage(cacheKey);
    
    if (cached) {
      console.log(`🖼️ Using cached image for: ${query}`);
      return [cached];
    }

    if (!this.checkRateLimit()) {
      console.warn('🚫 Unsplash rate limit reached, using fallback');
      return this.getFallbackImages(query);
    }

    try {
      this.requestCount++;
      
      const result = await unsplash.search.getPhotos({
        query,
        orientation: options.orientation,
        color: options.color,
        perPage: options.per_page || 10,
        page: options.page || 1,
      });

      if (result.errors) {
        console.error('Unsplash API errors:', result.errors);
        return this.getFallbackImages(query);
      }

      const images = result.response?.results || [];
      
      // Cache the first result
      if (images.length > 0) {
        this.cacheImage(cacheKey, images[0], query);
      }

      return images;
    } catch (error) {
      console.error('Error fetching from Unsplash:', error);
      return this.getFallbackImages(query);
    }
  }

  /**
   * Get a single random image by category
   */
  async getRandomImage(category?: string, options: ImageSearchOptions = {}): Promise<UnsplashImage | null> {
    const query = category || 'technology';
    const cacheKey = this.getCacheKey(`random-${query}`, options);
    const cached = this.getCachedImage(cacheKey);
    
    if (cached) {
      return cached;
    }

    if (!this.checkRateLimit()) {
      console.warn('🚫 Unsplash rate limit reached, using fallback');
      return this.getFallbackImages(query)[0] || null;
    }

    try {
      this.requestCount++;
      
      const result = await unsplash.photos.getRandom({
        query: category,
        orientation: options.orientation,
        count: 1,
      });

      if (result.errors) {
        console.error('Unsplash API errors:', result.errors);
        return null;
      }

      const image = Array.isArray(result.response) ? result.response[0] : result.response;
      
      if (image) {
        this.cacheImage(cacheKey, image, `random-${category}`);
        return image;
      }

      return null;
    } catch (error) {
      console.error('Error fetching random image from Unsplash:', error);
      return null;
    }
  }

  /**
   * Get image by ID
   */
  async getImageById(id: string): Promise<UnsplashImage | null> {
    const cacheKey = `id-${id}`;
    const cached = this.getCachedImage(cacheKey);
    
    if (cached) {
      return cached;
    }

    if (!this.checkRateLimit()) {
      return null;
    }

    try {
      this.requestCount++;
      
      const result = await unsplash.photos.get({ photoId: id });

      if (result.errors) {
        console.error('Unsplash API errors:', result.errors);
        return null;
      }

      const image = result.response;
      
      if (image) {
        this.cacheImage(cacheKey, image, `id-${id}`);
        return image;
      }

      return null;
    } catch (error) {
      console.error('Error fetching image by ID from Unsplash:', error);
      return null;
    }
  }

  /**
   * Fallback images when API is unavailable
   */
  private getFallbackImages(query: string): UnsplashImage[] {
    // Return placeholder images based on query context
    const fallbackImage: UnsplashImage = {
      id: `fallback-${Date.now()}`,
      urls: {
        raw: `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=800&fit=crop`,
        full: `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1200&h=800&fit=crop`,
        regular: `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1080&h=720&fit=crop`,
        small: `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop`,
        thumb: `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=150&fit=crop`,
      },
      alt_description: `Fallback image for ${query}`,
      description: `A beautiful image related to ${query}`,
      user: {
        name: 'Unsplash',
        username: 'unsplash',
        links: {
          html: 'https://unsplash.com/@unsplash',
        },
      },
      links: {
        html: 'https://unsplash.com/photos/technology',
        download_location: '',
      },
      width: 1200,
      height: 800,
    };

    return [fallbackImage];
  }

  /**
   * Track image download for Unsplash analytics
   */
  async trackDownload(downloadLocation: string): Promise<void> {
    if (!downloadLocation || !this.checkRateLimit()) return;

    try {
      this.requestCount++;
      await unsplash.photos.trackDownload({ downloadLocation });
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache(): void {
    imageCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: imageCache.size,
      keys: Array.from(imageCache.keys()),
    };
  }
}

// Export singleton instance
export const unsplashService = UnsplashService.getInstance();
