import React, { useState, useEffect } from 'react';
import { CheckCircle, Loader2, Clock, FileText, Package, Settings, Code, Folder } from 'lucide-react';

export interface GenerationStep {
  id: string;
  status: 'pending' | 'generating' | 'complete' | 'error';
  title: string;
  description: string;
  file?: string;
  type: 'setup' | 'install' | 'file' | 'config';
  duration?: number;
}

interface GenerationProgressProps {
  steps: GenerationStep[];
  currentStep?: string;
  onStepComplete?: (stepId: string) => void;
  className?: string;
}

const getStepIcon = (step: GenerationStep) => {
  const iconClass = "w-5 h-5";
  
  switch (step.status) {
    case 'complete':
      return <CheckCircle className={`${iconClass} text-green-500`} />;
    case 'generating':
      return <Loader2 className={`${iconClass} text-blue-500 animate-spin`} />;
    case 'error':
      return <CheckCircle className={`${iconClass} text-red-500`} />;
    default:
      return <Clock className={`${iconClass} text-gray-400`} />;
  }
};

const getTypeIcon = (type: string) => {
  const iconClass = "w-4 h-4 text-gray-500";
  
  switch (type) {
    case 'setup':
      return <Folder className={iconClass} />;
    case 'install':
      return <Package className={iconClass} />;
    case 'file':
      return <FileText className={iconClass} />;
    case 'config':
      return <Settings className={iconClass} />;
    default:
      return <Code className={iconClass} />;
  }
};

export const GenerationProgress: React.FC<GenerationProgressProps> = ({
  steps,
  currentStep,
  onStepComplete,
  className = ""
}) => {
  const [animatedSteps, setAnimatedSteps] = useState<Set<string>>(new Set());

  useEffect(() => {
    steps.forEach(step => {
      if (step.status === 'complete' && !animatedSteps.has(step.id)) {
        setTimeout(() => {
          setAnimatedSteps(prev => new Set([...prev, step.id]));
          onStepComplete?.(step.id);
        }, 100);
      }
    });
  }, [steps, animatedSteps, onStepComplete]);

  const completedSteps = steps.filter(step => step.status === 'complete').length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <div className={`bg-white rounded-lg border shadow-sm p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Generating Project
          </h3>
          <p className="text-sm text-gray-600">
            {completedSteps} of {totalSteps} steps completed
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">
            {Math.round(progressPercentage)}%
          </div>
          <div className="text-xs text-gray-500">Complete</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Steps List */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`
              flex items-start gap-3 p-3 rounded-lg transition-all duration-300
              ${step.status === 'generating' ? 'bg-blue-50 border border-blue-200' : ''}
              ${step.status === 'complete' ? 'bg-green-50' : ''}
              ${animatedSteps.has(step.id) ? 'animate-pulse' : ''}
            `}
          >
            {/* Status Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getStepIcon(step)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                {getTypeIcon(step.type)}
                <h4 className={`
                  text-sm font-medium truncate
                  ${step.status === 'complete' ? 'text-green-700' : 'text-gray-900'}
                  ${step.status === 'generating' ? 'text-blue-700' : ''}
                `}>
                  {step.title}
                </h4>
                {step.file && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {step.file}
                  </span>
                )}
              </div>
              <p className={`
                text-xs leading-relaxed
                ${step.status === 'complete' ? 'text-green-600' : 'text-gray-600'}
                ${step.status === 'generating' ? 'text-blue-600' : ''}
              `}>
                {step.description}
              </p>
              
              {/* Duration for completed steps */}
              {step.status === 'complete' && step.duration && (
                <div className="text-xs text-gray-400 mt-1">
                  Completed in {step.duration}ms
                </div>
              )}
            </div>

            {/* Step Number */}
            <div className="flex-shrink-0">
              <span className={`
                inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium
                ${step.status === 'complete' ? 'bg-green-100 text-green-700' : ''}
                ${step.status === 'generating' ? 'bg-blue-100 text-blue-700' : ''}
                ${step.status === 'pending' ? 'bg-gray-100 text-gray-500' : ''}
              `}>
                {index + 1}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Status */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        {completedSteps === totalSteps ? (
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm font-medium">
              Project generation complete! 🎉
            </span>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-blue-600">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span className="text-sm font-medium">
              Generating your project...
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

// Predefined step templates for common project types
export const createProjectSteps = (projectType: string, projectName: string): GenerationStep[] => {
  const baseSteps: GenerationStep[] = [
    {
      id: 'init',
      status: 'pending',
      title: 'Initialize Project',
      description: 'Creating project structure and folders',
      type: 'setup'
    },
    {
      id: 'package-json',
      status: 'pending',
      title: 'Generate package.json',
      description: 'Setting up project dependencies and scripts',
      file: 'package.json',
      type: 'config'
    },
    {
      id: 'install',
      status: 'pending',
      title: 'Install Dependencies',
      description: 'Installing React, TypeScript, and other packages',
      type: 'install'
    },
    {
      id: 'tsconfig',
      status: 'pending',
      title: 'Generate tsconfig.json',
      description: 'Configuring TypeScript compiler options',
      file: 'tsconfig.json',
      type: 'config'
    },
    {
      id: 'vite-config',
      status: 'pending',
      title: 'Generate vite.config.ts',
      description: 'Setting up Vite build configuration',
      file: 'vite.config.ts',
      type: 'config'
    },
    {
      id: 'tailwind-config',
      status: 'pending',
      title: 'Generate tailwind.config.js',
      description: 'Configuring Tailwind CSS framework',
      file: 'tailwind.config.js',
      type: 'config'
    },
    {
      id: 'index-html',
      status: 'pending',
      title: 'Generate index.html',
      description: 'Creating main HTML template',
      file: 'index.html',
      type: 'file'
    },
    {
      id: 'main-tsx',
      status: 'pending',
      title: 'Generate src/main.tsx',
      description: 'Creating React application entry point',
      file: 'src/main.tsx',
      type: 'file'
    },
    {
      id: 'app-tsx',
      status: 'pending',
      title: 'Generate src/App.tsx',
      description: 'Building main application component',
      file: 'src/App.tsx',
      type: 'file'
    }
  ];

  // Add project-specific components
  if (projectType === 'agency') {
    baseSteps.push(
      {
        id: 'header',
        status: 'pending',
        title: 'Generate src/components/Header.tsx',
        description: 'Building responsive navigation component',
        file: 'src/components/Header.tsx',
        type: 'file'
      },
      {
        id: 'hero',
        status: 'pending',
        title: 'Generate src/components/Hero.tsx',
        description: 'Creating hero section with CTA',
        file: 'src/components/Hero.tsx',
        type: 'file'
      },
      {
        id: 'services',
        status: 'pending',
        title: 'Generate src/components/Services.tsx',
        description: 'Building services showcase section',
        file: 'src/components/Services.tsx',
        type: 'file'
      },
      {
        id: 'footer',
        status: 'pending',
        title: 'Generate src/components/Footer.tsx',
        description: 'Creating footer with contact information',
        file: 'src/components/Footer.tsx',
        type: 'file'
      }
    );
  }

  baseSteps.push(
    {
      id: 'styles',
      status: 'pending',
      title: 'Generate src/styles/globals.css',
      description: 'Setting up global styles and Tailwind imports',
      file: 'src/styles/globals.css',
      type: 'file'
    },
    {
      id: 'readme',
      status: 'pending',
      title: 'Generate README.md',
      description: 'Adding project documentation and setup instructions',
      file: 'README.md',
      type: 'file'
    },
    {
      id: 'gitignore',
      status: 'pending',
      title: 'Generate .gitignore',
      description: 'Setting up Git ignore patterns',
      file: '.gitignore',
      type: 'config'
    }
  );

  return baseSteps;
};
