// Unsplash search patterns for different project types
export const UNSPLASH_PATTERNS = {
  'e-commerce': {
    primary: ['shopping', 'retail store', 'products display', 'commerce'],
    hero: ['modern shopping', 'clean retail space'],
    products: ['products on white background', 'minimalist product photography'],
    categories: ['fashion', 'electronics', 'home decor', 'accessories'],
  },
  'portfolio': {
    primary: ['creative workspace', 'design studio', 'professional portfolio'],
    hero: ['minimalist design', 'creative professional'],
    gallery: ['creative work', 'art gallery', 'design showcase'],
    about: ['professional headshot', 'creative professional portrait'],
  },
  'blog': {
    primary: ['writing workspace', 'content creation', 'blog writing'],
    hero: ['minimalist writing setup', 'clean workspace'],
    articles: ['books and coffee', 'reading setup', 'writing tools'],
    author: ['writer portrait', 'content creator'],
  },
  'restaurant': {
    primary: ['restaurant interior', 'food photography', 'dining experience'],
    hero: ['fine dining', 'restaurant atmosphere'],
    menu: ['gourmet food', 'culinary art', 'food plating'],
    ambiance: ['restaurant lighting', 'dining room'],
  },
  'travel': {
    primary: ['travel destinations', 'adventure photography', 'scenic landscapes'],
    hero: ['travel adventure', 'destination photography'],
    destinations: ['beautiful landscapes', 'travel locations', 'scenic views'],
    experiences: ['travel activities', 'adventure sports'],
  },
  'fitness': {
    primary: ['fitness gym', 'workout equipment', 'healthy lifestyle'],
    hero: ['fitness motivation', 'gym workout'],
    classes: ['group fitness', 'yoga class', 'personal training'],
    equipment: ['gym equipment', 'fitness gear'],
  },
  'real-estate': {
    primary: ['modern house', 'real estate', 'luxury home'],
    hero: ['beautiful home exterior', 'modern architecture'],
    properties: ['house interior', 'home design', 'property showcase'],
    locations: ['neighborhood', 'city skyline', 'residential area'],
  },
  'technology': {
    primary: ['technology workspace', 'coding setup', 'software development'],
    hero: ['modern office', 'tech startup'],
    products: ['software interface', 'technology devices'],
    team: ['tech team', 'developers working'],
  },
  'healthcare': {
    primary: ['medical professional', 'healthcare facility', 'wellness'],
    hero: ['modern clinic', 'medical care'],
    services: ['medical equipment', 'healthcare services'],
    staff: ['doctor portrait', 'medical team'],
  },
  'education': {
    primary: ['learning environment', 'education technology', 'students studying'],
    hero: ['classroom', 'online learning'],
    courses: ['books and learning', 'educational materials'],
    instructors: ['teacher portrait', 'education professional'],
  },
  'finance': {
    primary: ['financial planning', 'business meeting', 'professional office'],
    hero: ['modern office', 'business professional'],
    services: ['financial charts', 'business analytics'],
    team: ['business team', 'financial advisor'],
  },
  'default': {
    primary: ['modern minimalist', 'clean professional', 'business workspace'],
    hero: ['professional office', 'modern workspace'],
    content: ['clean design', 'minimalist setup'],
    team: ['professional team', 'business meeting'],
  },
};

export function getUnsplashPattern(projectType: string, context: string = 'primary'): string[] {
  const normalizedType = projectType.toLowerCase().replace(/[^a-z0-9]/g, '-');
  
  // Find matching pattern
  for (const [key, patterns] of Object.entries(UNSPLASH_PATTERNS)) {
    if (normalizedType.includes(key) || key.includes(normalizedType)) {
      return patterns[context as keyof typeof patterns] || patterns.primary;
    }
  }
  
  // Return default pattern
  return UNSPLASH_PATTERNS.default[context as keyof typeof UNSPLASH_PATTERNS.default] || UNSPLASH_PATTERNS.default.primary;
}

export function detectProjectType(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();
  
  // Project type detection patterns
  const typePatterns = {
    'e-commerce': ['shop', 'store', 'ecommerce', 'e-commerce', 'product', 'cart', 'checkout', 'buy', 'sell'],
    'portfolio': ['portfolio', 'showcase', 'personal site', 'resume', 'cv', 'work showcase'],
    'blog': ['blog', 'article', 'content', 'writing', 'news', 'magazine', 'journal'],
    'restaurant': ['restaurant', 'food', 'menu', 'dining', 'cafe', 'bistro', 'culinary'],
    'travel': ['travel', 'tour', 'vacation', 'booking', 'hotel', 'destination', 'trip'],
    'fitness': ['fitness', 'gym', 'workout', 'health', 'exercise', 'training', 'wellness'],
    'real-estate': ['real estate', 'property', 'house', 'home', 'apartment', 'rent', 'buy'],
    'technology': ['tech', 'software', 'app', 'saas', 'startup', 'development', 'coding'],
    'healthcare': ['health', 'medical', 'doctor', 'clinic', 'hospital', 'treatment'],
    'education': ['education', 'course', 'learning', 'school', 'university', 'training'],
    'finance': ['finance', 'banking', 'investment', 'trading', 'financial', 'accounting'],
  };
  
  for (const [type, keywords] of Object.entries(typePatterns)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      return type;
    }
  }
  
  return 'default';
}