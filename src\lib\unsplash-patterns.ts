import { ImageSearchOptions, unsplashService, UnsplashImage } from './unsplash';

/**
 * Context-aware image selection patterns
 */
export interface ImageContext {
  type: 'project' | 'landing' | 'error' | 'profile' | 'fragment' | 'placeholder';
  content?: string;
  category?: string;
  mood?: 'professional' | 'creative' | 'minimal' | 'vibrant' | 'dark' | 'light';
  aspectRatio?: 'square' | 'landscape' | 'portrait';
}

/**
 * Smart image selector that chooses appropriate images based on context
 */
export class UnsplashPatterns {
  private static instance: UnsplashPatterns;

  private constructor() {}

  static getInstance(): UnsplashPatterns {
    if (!UnsplashPatterns.instance) {
      UnsplashPatterns.instance = new UnsplashPatterns();
    }
    return UnsplashPatterns.instance;
  }

  /**
   * Get contextual image based on content and type
   */
  async getContextualImage(context: ImageContext): Promise<UnsplashImage | null> {
    const searchOptions = this.buildSearchOptions(context);
    const query = this.generateSmartQuery(context);

    console.log(`🎯 Getting contextual image for: ${context.type} - ${query}`);

    const images = await unsplashService.searchImages(query, searchOptions);
    return images.length > 0 ? images[0] : null;
  }

  /**
   * Generate smart search query based on context
   */
  private generateSmartQuery(context: ImageContext): string {
    const { type, content, category, mood } = context;

    // Base queries for different types
    const baseQueries: Record<string, string[]> = {
      project: ['coding', 'development', 'technology', 'programming', 'software'],
      landing: ['technology', 'innovation', 'digital', 'modern', 'abstract'],
      error: ['abstract', 'minimal', 'geometric', 'calm', 'peaceful'],
      profile: ['abstract', 'gradient', 'minimal', 'geometric', 'pattern'],
      fragment: ['code', 'screen', 'interface', 'digital', 'technology'],
      placeholder: ['abstract', 'minimal', 'texture', 'pattern', 'gradient'],
    };

    // Content-based keyword extraction
    const contentKeywords = this.extractKeywords(content || '');
    
    // Mood-based modifiers
    const moodModifiers: Record<string, string[]> = {
      professional: ['business', 'clean', 'corporate', 'minimal'],
      creative: ['artistic', 'colorful', 'vibrant', 'creative'],
      minimal: ['minimal', 'simple', 'clean', 'white'],
      vibrant: ['colorful', 'bright', 'energetic', 'dynamic'],
      dark: ['dark', 'moody', 'dramatic', 'night'],
      light: ['bright', 'airy', 'light', 'white'],
    };

    // Build query components
    const baseQuery = baseQueries[type] || baseQueries.placeholder;
    const moodQuery = mood ? moodModifiers[mood] || [] : [];
    const categoryQuery = category ? [category] : [];

    // Combine and prioritize
    const allKeywords = [
      ...contentKeywords.slice(0, 2), // Top 2 content keywords
      ...categoryQuery,
      ...baseQuery.slice(0, 2), // Top 2 base keywords
      ...moodQuery.slice(0, 1), // Top 1 mood keyword
    ];

    // Return the most relevant query
    return allKeywords.length > 0 ? allKeywords[0] : 'technology';
  }

  /**
   * Extract relevant keywords from content
   */
  private extractKeywords(content: string): string[] {
    if (!content) return [];

    // Common tech/development keywords
    const techKeywords = [
      'react', 'vue', 'angular', 'javascript', 'typescript', 'python', 'java',
      'web', 'mobile', 'app', 'website', 'dashboard', 'api', 'database',
      'ecommerce', 'blog', 'portfolio', 'landing', 'admin', 'cms',
      'ai', 'machine learning', 'data', 'analytics', 'blockchain',
      'game', 'animation', 'design', 'ui', 'ux', 'interface'
    ];

    // Industry keywords
    const industryKeywords = [
      'finance', 'healthcare', 'education', 'retail', 'travel', 'food',
      'music', 'sports', 'news', 'social', 'business', 'startup'
    ];

    // Visual keywords
    const visualKeywords = [
      'modern', 'minimal', 'dark', 'light', 'colorful', 'gradient',
      'abstract', 'geometric', 'nature', 'urban', 'space', 'ocean'
    ];

    const allKeywords = [...techKeywords, ...industryKeywords, ...visualKeywords];
    const contentLower = content.toLowerCase();
    
    return allKeywords
      .filter(keyword => contentLower.includes(keyword))
      .sort((a, b) => {
        // Prioritize by frequency and position
        const aCount = (contentLower.match(new RegExp(a, 'g')) || []).length;
        const bCount = (contentLower.match(new RegExp(b, 'g')) || []).length;
        const aPos = contentLower.indexOf(a);
        const bPos = contentLower.indexOf(b);
        
        if (aCount !== bCount) return bCount - aCount;
        return aPos - bPos;
      });
  }

  /**
   * Build search options based on context
   */
  private buildSearchOptions(context: ImageContext): ImageSearchOptions {
    const options: ImageSearchOptions = {
      per_page: 10,
      page: 1,
    };

    // Set orientation based on aspect ratio
    if (context.aspectRatio) {
      switch (context.aspectRatio) {
        case 'square':
          options.orientation = 'squarish';
          break;
        case 'landscape':
          options.orientation = 'landscape';
          break;
        case 'portrait':
          options.orientation = 'portrait';
          break;
      }
    }

    // Set color based on mood
    if (context.mood) {
      switch (context.mood) {
        case 'dark':
          options.color = 'black';
          break;
        case 'light':
          options.color = 'white';
          break;
        case 'minimal':
          options.color = 'black_and_white';
          break;
        case 'vibrant':
          // Let Unsplash choose colorful images
          break;
      }
    }

    // Set size based on type
    switch (context.type) {
      case 'landing':
        options.size = 'full';
        break;
      case 'project':
        options.size = 'regular';
        break;
      case 'profile':
      case 'placeholder':
        options.size = 'small';
        break;
      default:
        options.size = 'regular';
    }

    return options;
  }

  /**
   * Get project thumbnail image
   */
  async getProjectThumbnail(projectName: string, content?: string): Promise<UnsplashImage | null> {
    return this.getContextualImage({
      type: 'project',
      content: `${projectName} ${content || ''}`,
      mood: 'professional',
      aspectRatio: 'landscape',
    });
  }

  /**
   * Get landing page hero image
   */
  async getLandingHeroImage(theme: 'light' | 'dark' = 'light'): Promise<UnsplashImage | null> {
    return this.getContextualImage({
      type: 'landing',
      category: 'technology innovation',
      mood: theme === 'dark' ? 'dark' : 'light',
      aspectRatio: 'landscape',
    });
  }

  /**
   * Get error page background
   */
  async getErrorPageImage(errorType: '404' | '500' | 'general' = 'general'): Promise<UnsplashImage | null> {
    const categories = {
      '404': 'lost path maze',
      '500': 'broken abstract',
      'general': 'calm peaceful'
    };

    return this.getContextualImage({
      type: 'error',
      category: categories[errorType],
      mood: 'minimal',
      aspectRatio: 'landscape',
    });
  }

  /**
   * Get profile/avatar placeholder
   */
  async getProfilePlaceholder(username?: string): Promise<UnsplashImage | null> {
    return this.getContextualImage({
      type: 'profile',
      content: username,
      mood: 'minimal',
      aspectRatio: 'square',
    });
  }

  /**
   * Get fragment preview image based on generated content
   */
  async getFragmentPreview(fragmentContent: string, title?: string): Promise<UnsplashImage | null> {
    return this.getContextualImage({
      type: 'fragment',
      content: `${title || ''} ${fragmentContent}`,
      mood: 'professional',
      aspectRatio: 'landscape',
    });
  }

  /**
   * Get images for AI-generated content
   */
  async getAIContentImages(prompt: string, count: number = 3): Promise<UnsplashImage[]> {
    const context: ImageContext = {
      type: 'fragment',
      content: prompt,
      mood: 'creative',
      aspectRatio: 'landscape',
    };

    const query = this.generateSmartQuery(context);
    const options = this.buildSearchOptions(context);
    options.per_page = Math.min(count, 10);

    return await unsplashService.searchImages(query, options);
  }

  /**
   * Get themed image collection
   */
  async getThemedCollection(theme: string, count: number = 5): Promise<UnsplashImage[]> {
    const options: ImageSearchOptions = {
      per_page: Math.min(count, 10),
      orientation: 'landscape',
      size: 'regular',
    };

    return await unsplashService.searchImages(theme, options);
  }
}

// Export singleton instance
export const unsplashPatterns = UnsplashPatterns.getInstance();

// Convenience functions for common use cases
export const getProjectImage = (projectName: string, content?: string) => 
  unsplashPatterns.getProjectThumbnail(projectName, content);

export const getLandingImage = (theme?: 'light' | 'dark') => 
  unsplashPatterns.getLandingHeroImage(theme);

export const getErrorImage = (errorType?: '404' | '500' | 'general') => 
  unsplashPatterns.getErrorPageImage(errorType);

export const getProfileImage = (username?: string) => 
  unsplashPatterns.getProfilePlaceholder(username);

export const getFragmentImage = (content: string, title?: string) => 
  unsplashPatterns.getFragmentPreview(content, title);

export const getAIImages = (prompt: string, count?: number) => 
  unsplashPatterns.getAIContentImages(prompt, count);
