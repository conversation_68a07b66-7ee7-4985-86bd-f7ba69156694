"use client";

import { AIGenerationSystem } from '@/components/ai-generation-system';

export default function DemoPage() {
  const handleGenerationComplete = (files: Record<string, string>) => {
    console.log('Generation complete!', files);
    // In a real implementation, you could:
    // - Save files to a project
    // - Navigate to the generated project
    // - Show success notification
  };

  return (
    <div className="min-h-screen bg-gray-950">
      <AIGenerationSystem
        projectPrompt="Create a modern e-commerce website with product listings, shopping cart, and checkout flow. Include high-quality product images, a responsive design, and smooth user interactions."
        onComplete={handleGenerationComplete}
      />
    </div>
  );
}