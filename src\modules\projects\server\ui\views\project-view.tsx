"use client";

import { 
    Resizable<PERSON>andle,
    ResizablePanelGroup,
    ResizablePanel,
 } from "@/components/ui/resizable";
import { MessagesContainer } from "../components/messages-container";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Suspense, useState } from "react";
import { Fragment } from "@/generated/prisma";
import { ProjectHeader } from "../components/project-header";
import { FragmentWeb } from "../components/fragment-web";
import { EyeIcon, CodeIcon, CrownIcon, BarChart3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { FileExplorer } from "@/components/file-explorer";
import { UserControl } from "@/components/user-control";
import { useAuth } from "@clerk/nextjs";
import { ErrorBoundary } from "react-error-boundary";
import { TokenUsageStats } from "@/components/token-usage-stats";

interface Props{
    projectId: string;
};
export const ProjectView = ({projectId}: Props) =>{
    const {has} = useAuth();
    const hasProAccess = has?.({ plan: "pro" });
    const [activeFragment, setActiveFragment] = useState<Fragment | null>(null);
    const [tabState, setTabState] = useState<"preview" | "code" | "stats">("preview");

return(
    <div className="h-screen">
        <ResizablePanelGroup direction="horizontal">
            <ResizablePanel 
            defaultSize={35}
            minSize={35}
            className="flex flex-col min-h-0"
            >
                <ErrorBoundary fallback={<p>Project header error</p>}>
                    <Suspense fallback={<p>Loading project...</p>}>
                    <ProjectHeader projectId={projectId}/>
                    </Suspense>
                </ErrorBoundary>
                <ErrorBoundary fallback={<p>Messages error</p>}>
                    <Suspense fallback= {<p>Loading messages...</p>}>
                        <MessagesContainer 
                        projectId={projectId}
                        activeFragment = {activeFragment}
                        setActiveFragment = {setActiveFragment}

                        />
                    </Suspense>
                </ErrorBoundary>
            </ResizablePanel>
            <ResizableHandle className="hover:bg-primary transition-colors"/>
            <ResizablePanel
            defaultSize={65}
            minSize={50}
            >
                <Tabs
                className="h-full gap-y-0"
                defaultValue = "preview"
                value = {tabState}
                onValueChange={(value) => setTabState(value as "preview" | "code" | "stats")}
                >
                    <div className="w-full flex items-center p-2 border-b gap-x-2">
                        <TabsList className="h-8 p-0 border rounded-md">
                            <TabsTrigger value="preview" className="rounded-md">
                                <EyeIcon /> <span>Demo</span>
                            </TabsTrigger>
                            <TabsTrigger value="code" className="rounded-md">
                                <CodeIcon /> <span>Code</span>
                            </TabsTrigger>
                            <TabsTrigger value="stats" className="rounded-md">
                                <BarChart3 /> <span>Stats</span>
                            </TabsTrigger>
                        </TabsList>
                        <div className="ml-auto flex items-center gap-x-2">
                            {!hasProAccess && (
                                <Button asChild size="sm" variant = "default">
                                    <Link href="/pricing">
                                        <CrownIcon /> Upgrade
                                    </Link>
                                </Button>
                            )}
                            <UserControl />
                        </div>
                    </div>
                    <TabsContent value="preview">
                        {!!activeFragment && <FragmentWeb data = {activeFragment}/>}
                    </TabsContent>
                    <TabsContent value="code" className="min-h-0">
                        {!!activeFragment?.files && (
                            <FileExplorer
                            files = {activeFragment.files as {[path: string]: string}}
                            />
                        )}
                    </TabsContent>
                    <TabsContent value="stats" className="min-h-0 p-4 overflow-auto">
                        <ErrorBoundary fallback={<p>Token usage stats error</p>}>
                            <Suspense fallback={<p>Loading token usage stats...</p>}>
                                <TokenUsageStats projectId={projectId} showUserStats={true} />
                            </Suspense>
                        </ErrorBoundary>
                    </TabsContent>
                </Tabs>
            </ResizablePanel>
        </ResizablePanelGroup>
    </div>
)
};