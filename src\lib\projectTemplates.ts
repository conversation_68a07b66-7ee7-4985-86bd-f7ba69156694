import { GenerationStep } from '../components/GenerationProgress';
import { FileNode } from '../components/EnhancedFileTree';
import { PackageInfo } from '../components/NpmInstallVisualization';

export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  features: string[];
  technologies: string[];
  steps: GenerationStep[];
  files: FileNode[];
  packages: PackageInfo[];
  estimatedTime: number; // in seconds
}

export const projectTemplates: Record<string, ProjectTemplate> = {
  'modern-agency': {
    id: 'modern-agency',
    name: 'Modern Creative Digital Agency',
    description: 'A stunning, responsive website for creative agencies with modern design and smooth animations',
    category: 'Business',
    features: [
      'Responsive Design',
      'Modern UI/UX',
      'Smooth Animations',
      'Contact Forms',
      'Portfolio Showcase',
      'SEO Optimized'
    ],
    technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Vite', 'Lucide Icons'],
    estimatedTime: 45,
    steps: [
      {
        id: 'init',
        status: 'pending',
        title: 'Initialize Project Structure',
        description: 'Creating project folders and basic structure',
        type: 'setup'
      },
      {
        id: 'package-json',
        status: 'pending',
        title: 'Generate package.json',
        description: 'Setting up project dependencies and scripts',
        file: 'package.json',
        type: 'config'
      },
      {
        id: 'install-deps',
        status: 'pending',
        title: 'Install Dependencies',
        description: 'Installing React, TypeScript, Tailwind CSS and other packages',
        type: 'install'
      },
      {
        id: 'tsconfig',
        status: 'pending',
        title: 'Generate tsconfig.json',
        description: 'Configuring TypeScript compiler options',
        file: 'tsconfig.json',
        type: 'config'
      },
      {
        id: 'vite-config',
        status: 'pending',
        title: 'Generate vite.config.ts',
        description: 'Setting up Vite build configuration with React plugin',
        file: 'vite.config.ts',
        type: 'config'
      },
      {
        id: 'tailwind-config',
        status: 'pending',
        title: 'Generate tailwind.config.js',
        description: 'Configuring Tailwind CSS with custom theme',
        file: 'tailwind.config.js',
        type: 'config'
      },
      {
        id: 'postcss-config',
        status: 'pending',
        title: 'Generate postcss.config.js',
        description: 'Setting up PostCSS with Tailwind and Autoprefixer',
        file: 'postcss.config.js',
        type: 'config'
      },
      {
        id: 'index-html',
        status: 'pending',
        title: 'Generate index.html',
        description: 'Creating main HTML template with meta tags',
        file: 'index.html',
        type: 'file'
      },
      {
        id: 'main-tsx',
        status: 'pending',
        title: 'Generate src/main.tsx',
        description: 'Creating React application entry point',
        file: 'src/main.tsx',
        type: 'file'
      },
      {
        id: 'app-tsx',
        status: 'pending',
        title: 'Generate src/App.tsx',
        description: 'Building main application component with routing',
        file: 'src/App.tsx',
        type: 'file'
      },
      {
        id: 'header-component',
        status: 'pending',
        title: 'Generate src/components/Header.tsx',
        description: 'Building responsive navigation header with mobile menu',
        file: 'src/components/Header.tsx',
        type: 'file'
      },
      {
        id: 'hero-component',
        status: 'pending',
        title: 'Generate src/components/Hero.tsx',
        description: 'Creating hero section with call-to-action',
        file: 'src/components/Hero.tsx',
        type: 'file'
      },
      {
        id: 'services-component',
        status: 'pending',
        title: 'Generate src/components/Services.tsx',
        description: 'Building services showcase with icons and descriptions',
        file: 'src/components/Services.tsx',
        type: 'file'
      },
      {
        id: 'portfolio-component',
        status: 'pending',
        title: 'Generate src/components/Portfolio.tsx',
        description: 'Creating portfolio grid with project showcases',
        file: 'src/components/Portfolio.tsx',
        type: 'file'
      },
      {
        id: 'about-component',
        status: 'pending',
        title: 'Generate src/components/About.tsx',
        description: 'Building about section with team information',
        file: 'src/components/About.tsx',
        type: 'file'
      },
      {
        id: 'contact-component',
        status: 'pending',
        title: 'Generate src/components/Contact.tsx',
        description: 'Creating contact form with validation',
        file: 'src/components/Contact.tsx',
        type: 'file'
      },
      {
        id: 'footer-component',
        status: 'pending',
        title: 'Generate src/components/Footer.tsx',
        description: 'Building footer with social links and contact info',
        file: 'src/components/Footer.tsx',
        type: 'file'
      },
      {
        id: 'globals-css',
        status: 'pending',
        title: 'Generate src/styles/globals.css',
        description: 'Setting up global styles with Tailwind imports',
        file: 'src/styles/globals.css',
        type: 'file'
      },
      {
        id: 'eslint-config',
        status: 'pending',
        title: 'Generate eslint.config.js',
        description: 'Configuring ESLint for code quality',
        file: 'eslint.config.js',
        type: 'config'
      },
      {
        id: 'gitignore',
        status: 'pending',
        title: 'Generate .gitignore',
        description: 'Setting up Git ignore patterns for Node.js projects',
        file: '.gitignore',
        type: 'config'
      },
      {
        id: 'readme',
        status: 'pending',
        title: 'Generate README.md',
        description: 'Creating comprehensive project documentation',
        file: 'README.md',
        type: 'file'
      }
    ],
    files: [
      {
        id: 'root',
        name: 'modern-agency-website',
        type: 'folder',
        path: '/',
        status: 'complete',
        isOpen: true,
        children: [
          {
            id: 'src',
            name: 'src',
            type: 'folder',
            path: '/src',
            status: 'complete',
            isOpen: true,
            children: [
              {
                id: 'components',
                name: 'components',
                type: 'folder',
                path: '/src/components',
                status: 'complete',
                isOpen: true,
                children: [
                  {
                    id: 'header-tsx',
                    name: 'Header.tsx',
                    type: 'file',
                    path: '/src/components/Header.tsx',
                    status: 'pending',
                    size: 3200
                  },
                  {
                    id: 'hero-tsx',
                    name: 'Hero.tsx',
                    type: 'file',
                    path: '/src/components/Hero.tsx',
                    status: 'pending',
                    size: 2800
                  },
                  {
                    id: 'services-tsx',
                    name: 'Services.tsx',
                    type: 'file',
                    path: '/src/components/Services.tsx',
                    status: 'pending',
                    size: 4100
                  },
                  {
                    id: 'portfolio-tsx',
                    name: 'Portfolio.tsx',
                    type: 'file',
                    path: '/src/components/Portfolio.tsx',
                    status: 'pending',
                    size: 3600
                  },
                  {
                    id: 'about-tsx',
                    name: 'About.tsx',
                    type: 'file',
                    path: '/src/components/About.tsx',
                    status: 'pending',
                    size: 2400
                  },
                  {
                    id: 'contact-tsx',
                    name: 'Contact.tsx',
                    type: 'file',
                    path: '/src/components/Contact.tsx',
                    status: 'pending',
                    size: 3800
                  },
                  {
                    id: 'footer-tsx',
                    name: 'Footer.tsx',
                    type: 'file',
                    path: '/src/components/Footer.tsx',
                    status: 'pending',
                    size: 1800
                  }
                ]
              },
              {
                id: 'styles',
                name: 'styles',
                type: 'folder',
                path: '/src/styles',
                status: 'complete',
                children: [
                  {
                    id: 'globals-css',
                    name: 'globals.css',
                    type: 'file',
                    path: '/src/styles/globals.css',
                    status: 'pending',
                    size: 800
                  }
                ]
              },
              {
                id: 'app-tsx',
                name: 'App.tsx',
                type: 'file',
                path: '/src/App.tsx',
                status: 'pending',
                size: 1600
              },
              {
                id: 'main-tsx',
                name: 'main.tsx',
                type: 'file',
                path: '/src/main.tsx',
                status: 'pending',
                size: 400
              }
            ]
          },
          {
            id: 'public',
            name: 'public',
            type: 'folder',
            path: '/public',
            status: 'complete',
            children: [
              {
                id: 'index-html',
                name: 'index.html',
                type: 'file',
                path: '/public/index.html',
                status: 'pending',
                size: 800
              },
              {
                id: 'favicon-ico',
                name: 'favicon.ico',
                type: 'file',
                path: '/public/favicon.ico',
                status: 'pending',
                size: 1024
              }
            ]
          },
          {
            id: 'package-json',
            name: 'package.json',
            type: 'file',
            path: '/package.json',
            status: 'pending',
            size: 1200
          },
          {
            id: 'package-lock-json',
            name: 'package-lock.json',
            type: 'file',
            path: '/package-lock.json',
            status: 'pending',
            size: 85000
          },
          {
            id: 'tsconfig-json',
            name: 'tsconfig.json',
            type: 'file',
            path: '/tsconfig.json',
            status: 'pending',
            size: 600
          },
          {
            id: 'vite-config-ts',
            name: 'vite.config.ts',
            type: 'file',
            path: '/vite.config.ts',
            status: 'pending',
            size: 300
          },
          {
            id: 'tailwind-config-js',
            name: 'tailwind.config.js',
            type: 'file',
            path: '/tailwind.config.js',
            status: 'pending',
            size: 400
          },
          {
            id: 'postcss-config-js',
            name: 'postcss.config.js',
            type: 'file',
            path: '/postcss.config.js',
            status: 'pending',
            size: 150
          },
          {
            id: 'eslint-config-js',
            name: 'eslint.config.js',
            type: 'file',
            path: '/eslint.config.js',
            status: 'pending',
            size: 800
          },
          {
            id: 'gitignore',
            name: '.gitignore',
            type: 'file',
            path: '/.gitignore',
            status: 'pending',
            size: 300
          },
          {
            id: 'readme-md',
            name: 'README.md',
            type: 'file',
            path: '/README.md',
            status: 'pending',
            size: 2500
          }
        ]
      }
    ],
    packages: [
      {
        name: 'react',
        version: '18.2.0',
        description: 'A JavaScript library for building user interfaces',
        size: '2.5MB',
        status: 'pending'
      },
      {
        name: 'react-dom',
        version: '18.2.0',
        description: 'React package for working with the DOM',
        size: '1.8MB',
        status: 'pending'
      },
      {
        name: '@types/react',
        version: '18.2.15',
        description: 'TypeScript definitions for React',
        size: '0.3MB',
        status: 'pending'
      },
      {
        name: '@types/react-dom',
        version: '18.2.7',
        description: 'TypeScript definitions for React DOM',
        size: '0.2MB',
        status: 'pending'
      },
      {
        name: 'typescript',
        version: '5.0.2',
        description: 'TypeScript is a language for application scale JavaScript',
        size: '12.1MB',
        status: 'pending'
      },
      {
        name: 'vite',
        version: '4.4.5',
        description: 'Native-ESM powered web dev build tool',
        size: '8.7MB',
        status: 'pending'
      },
      {
        name: '@vitejs/plugin-react',
        version: '4.0.3',
        description: 'The all-in-one Vite plugin for React projects',
        size: '0.5MB',
        status: 'pending'
      },
      {
        name: 'tailwindcss',
        version: '3.3.3',
        description: 'A utility-first CSS framework',
        size: '4.2MB',
        status: 'pending'
      },
      {
        name: 'autoprefixer',
        version: '10.4.14',
        description: 'Parse CSS and add vendor prefixes automatically',
        size: '1.1MB',
        status: 'pending'
      },
      {
        name: 'postcss',
        version: '8.4.27',
        description: 'Tool for transforming CSS with JavaScript',
        size: '0.8MB',
        status: 'pending'
      },
      {
        name: 'lucide-react',
        version: '0.263.1',
        description: 'Beautiful & consistent icon toolkit',
        size: '1.2MB',
        status: 'pending'
      },
      {
        name: 'eslint',
        version: '8.45.0',
        description: 'An AST-based pattern checker for JavaScript',
        size: '3.4MB',
        status: 'pending'
      }
    ]
  }
};

export const getProjectTemplate = (templateId: string): ProjectTemplate | null => {
  return projectTemplates[templateId] || null;
};

export const getAllTemplates = (): ProjectTemplate[] => {
  return Object.values(projectTemplates);
};
