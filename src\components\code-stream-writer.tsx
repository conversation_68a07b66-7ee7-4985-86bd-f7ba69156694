"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Copy, 
  Download, 
  Play, 
  Pause,
  RotateCcw,
  FileIcon,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Basic syntax highlighting
import Prism from 'prismjs';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-css';

interface CodeStreamWriterProps {
  fileName: string;
  content: string;
  isStreaming?: boolean;
  language?: string;
  className?: string;
  onComplete?: () => void;
}

export const CodeStreamWriter: React.FC<CodeStreamWriterProps> = ({
  content,
  fileName,
  language = 'typescript',
  isStreaming = false,
  onComplete,
  className
}) => {
  const [displayedCode, setDisplayedCode] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(isStreaming);
  const [isComplete, setIsComplete] = useState(false);
  const [currentSpeed, setCurrentSpeed] = useState(50);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const codeRef = useRef<HTMLElement>(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    if (codeRef.current) {
      codeRef.current.scrollTop = codeRef.current.scrollHeight;
    }
  };

  // Reset when code changes
  useEffect(() => {
    if (code !== displayedCode) {
      setDisplayedCode('');
      setCurrentIndex(0);
      setIsComplete(false);
      setIsPlaying(autoStart);
    }
  }, [code, autoStart]);

  // Typewriter effect
  useEffect(() => {
    if (isPlaying && currentIndex < code.length && !isComplete) {
      const char = code[currentIndex];
      
      // Variable speed based on character type
      let charDelay = currentSpeed;
      if (char === '\n') {
        charDelay *= 2; // Pause at line breaks
      } else if (char === '{' || char === '}') {
        charDelay *= 1.5; // Slower for braces
      } else if (char === ' ') {
        charDelay *= 0.5; // Faster for spaces
      }

      intervalRef.current = setTimeout(() => {
        setDisplayedCode(prev => prev + char);
        setCurrentIndex(prev => prev + 1);
        scrollToBottom();
      }, charDelay);

      return () => {
        if (intervalRef.current) {
          clearTimeout(intervalRef.current);
        }
      };
    } else if (currentIndex >= code.length && !isComplete) {
      setIsComplete(true);
      setIsPlaying(false);
      onComplete?.();
    }
  }, [currentIndex, code, isPlaying, currentSpeed, isComplete, onComplete]);

  // Syntax highlighting
  useEffect(() => {
    if (codeRef.current && displayedCode) {
      try {
        const highlighted = Prism.highlight(
          displayedCode,
          Prism.languages[language] || Prism.languages.plain,
          language
        );
        codeRef.current.innerHTML = highlighted;
      } catch (error) {
        codeRef.current.textContent = displayedCode;
      }
    }
  }, [displayedCode, language]);

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const reset = () => {
    setDisplayedCode('');
    setCurrentIndex(0);
    setIsComplete(false);
    setIsPlaying(true);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const downloadFile = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const progress = code.length > 0 ? (currentIndex / code.length) * 100 : 0;
  const linesCount = displayedCode.split('\n').length;
  const charsCount = displayedCode.length;

  return (
    <Card className={cn("overflow-hidden bg-gray-950 border-gray-800", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800 bg-gray-900">
        <div className="flex items-center gap-3">
          {/* Terminal dots */}
          <div className="flex gap-1.5">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          
          <div className="flex items-center gap-2">
            <FileIcon className="w-4 h-4 text-gray-400" />
            <span className="text-white font-medium text-sm">{fileName}</span>
            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
              {language.toUpperCase()}
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Playback controls */}
          <div className="flex items-center gap-1 mr-3">
            <Button
              size="sm"
              variant="ghost"
              onClick={togglePlayPause}
              disabled={isComplete}
              className="h-8 w-8 p-0"
            >
              {isPlaying ? (
                <Pause className="w-4 h-4" />
              ) : (
                <Play className="w-4 h-4" />
              )}
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={reset}
              className="h-8 w-8 p-0"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>

            {/* Speed controls */}
            <div className="flex gap-1 ml-2">
              {[25, 50, 100].map(speedOption => (
                <Button
                  key={speedOption}
                  size="sm"
                  variant={currentSpeed === speedOption ? "default" : "ghost"}
                  onClick={() => setCurrentSpeed(speedOption)}
                  className="h-6 px-2 text-xs"
                >
                  {speedOption === 25 ? '0.5x' : speedOption === 50 ? '1x' : '2x'}
                </Button>
              ))}
            </div>
          </div>

          {/* File actions */}
          <Button
            size="sm"
            variant="ghost"
            onClick={copyToClipboard}
            className="h-8 w-8 p-0"
          >
            <Copy className="w-4 h-4" />
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={downloadFile}
            className="h-8 w-8 p-0"
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Progress bar */}
      {!isComplete && (
        <div className="h-1 bg-gray-800">
          <div 
            className="h-full bg-blue-500 transition-all duration-100"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      {/* Code content */}
      <div className="relative">
        <pre className={cn(
          "p-4 text-sm font-mono leading-relaxed h-96 overflow-auto",
          "bg-gray-950 text-gray-100",
          `language-${language}`
        )}>
          <code 
            ref={codeRef}
            className={`language-${language}`}
          >
            {displayedCode}
          </code>
          
          {/* Blinking cursor */}
          {isPlaying && !isComplete && (
            <span className="animate-pulse text-blue-400 ml-1 font-bold">|</span>
          )}
        </pre>
      </div>

      {/* Footer stats */}
      <div className="flex items-center justify-between p-3 border-t border-gray-800 bg-gray-900 text-xs text-gray-400">
        <div className="flex items-center gap-4">
          <span>{linesCount} lines</span>
          <span>{charsCount} characters</span>
          <span>{Math.round(currentSpeed)}ms/char</span>
        </div>
        
        <div className="flex items-center gap-2">
          {isPlaying && !isComplete && (
            <>
              <Loader2 className="w-3 h-3 animate-spin text-blue-400" />
              <span className="text-blue-400">Writing...</span>
            </>
          )}
          
          {isComplete && (
            <span className="text-green-400 flex items-center gap-1">
              <div className="w-2 h-2 rounded-full bg-green-400" />
              Complete
            </span>
          )}
          
          {!isPlaying && !isComplete && (
            <span className="text-yellow-400">Paused</span>
          )}
        </div>
      </div>
    </Card>
  );
};