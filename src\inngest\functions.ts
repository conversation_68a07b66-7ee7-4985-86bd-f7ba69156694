import { inngest } from "./client";
import { Sandbox } from "@e2b/code-interpreter";
import { openai, createAgent, createTool, createNetwork, type Tool, type Message, createState } from "@inngest/agent-kit";
import { getSandbox, lastAssistantTextMessageContent, parseAgentOutput } from "./utils";
import { z } from "zod";
import { FRAGMENT_TITLE_PROMPT, PROMPT, RESPONSE_PROMPT } from "@/prompt";
import { prisma } from "@/lib/db";
import { SANDBOX_TIMEOUT } from "./type";
import { trackAgentTokenUsage } from "@/lib/token-tracker";

interface AgentState {
  summary: string;
  files: {[path: string]: string };
};


export const codeAgentFunction = inngest.createFunction(
  { id: "code-agent" },
  { event: "code-agent/run" },
  async ({ event, step }) => {
    const sandboxId = await step.run("get-sandbox-id", async () => {
      const sandbox = await Sandbox.create("vibe-nextjs-test3");
      await sandbox.setTimeout(SANDBOX_TIMEOUT);
      return sandbox.sandboxId;
    });

    const previousMessages = await step.run("get-previous-messages", async() => {
      const formattedMessages: Message[] = [];

      const messages = await prisma.message.findMany({
        where: {
          projectId: event.data.projectId,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      });

      for (const message of messages) {
        formattedMessages.push({
          type: "text",
          role: message.role === "ASSISTANT" ? "assistant" : "user",
          content: message.content,
        });
      }

      return formattedMessages.reverse();
    })

    const state = createState<AgentState>(
      {
        summary: "",
        files: {},
    },
    {
      messages: previousMessages,
    },
  );

    const codeAgent = createAgent<AgentState>({
      name: "code-agent",
      description: "Coding Agent",
      system: PROMPT,
      model: openai({ model: "gpt-4o",
        defaultParameters: {
          temperature: 0.8,
        },
      }),

      tools: [
        createTool({
          name: "terminal",
          description: "Use the terminal to run commands",
          parameters: z.object({
            command: z.string(),
          }),
          handler: async ({ command }, { step }) => {
            return await step?.run("terminal", async () => {
              const buffers = { stdout: "", stderr: "" };

              try {
                const sandbox = await getSandbox(sandboxId);
                const result = await sandbox.commands.run(command, {
                  onStdout: (data: string) => {
                    buffers.stdout += data;
                  },
                  onStderr: (data: string) => {
                    buffers.stderr += data;
                  }
                });
                return result.stdout;
              } catch (e) {
                console.error(
                  `Command failed: ${e} \nstdout: ${buffers.stdout}\nstderror: ${buffers.stderr}`,);
                return `Command failed: ${e} \nstdout: ${buffers.stdout}\nstderror: ${buffers.stderr}`;
              }

            });
          },
        }),
        createTool({
          name: "createOrUpdateFiles",
          description: "Create or Update Files",
          parameters: z.object({
            files: z.array(
            z.object({
              path: z.string(),
              content: z.string(),
            })
          ),
        }),
        handler: async (
          { files },
          { step, network }: Tool.Options<AgentState>
        ) => {
          const newFiles = await step?.run("createOrUpdateFiles", async () => {
            try {
              const updatedFiles = network.state.data.files || {};
              const sandbox = await getSandbox(sandboxId);
              for (const file of files) {
                await sandbox.files.write(file.path, file.content);
                updatedFiles[file.path] = file.content;
              }
              return updatedFiles;
            } catch (e) {
              return "Error: " + e;
            }
          });

          if (typeof newFiles === "object") {
            network.state.data.files = newFiles;
          }
        },
  }),
  createTool({
    name: "readFiles",
    description: "Read files from the sandbox",
    parameters: z.object({
      files: z.array(z.string()),
    }),
    handler: async ({ files }, { step }) => {
      return await step?.run("readFiles", async () => {
        try {
          const sandbox = await getSandbox(sandboxId);
          const contents = [];
          for (const file of files) {
            const content = await sandbox.files.read(file);
            contents.push({ path: file, content });
          }
          return JSON.stringify(contents);
        } catch (e) {
          return "Error: " + e;
        }
        });
      }
    }),
  ],
  lifecycle: {
    onResponse: async ({ result, network }) => {
      const lastAssistantMessageText = lastAssistantTextMessageContent(result);
      if (lastAssistantMessageText && network) {
        if (lastAssistantMessageText.includes("<task_summary>")){
          network.state.data.summary = lastAssistantMessageText
        }
      }
      return result;

    },
  },
  });

  const network = createNetwork<AgentState>({
    name: "coding-agent-network",
    agents: [codeAgent],
    maxIter: 15,
    defaultState: state,
    router: async ({ network }) => {
      const summary = network.state.data.summary;
      if (summary) {
        return;
      }
      return codeAgent;
    },
  });

  // Track main agent execution
  const mainAgentStartTime = Date.now();
  console.log(`🚀 Starting main coding agent network execution`);

  const result = await network.run(event.data.value, {state});

  const mainAgentEndTime = Date.now();
  const mainAgentDuration = mainAgentEndTime - mainAgentStartTime;

  // Extract input and output for token tracking
  const inputText = event.data.value;
  const outputText = result.state.data.summary || '';

  // Track token usage for main agent
  await trackAgentTokenUsage(
    event.data.projectId,
    'code-agent',
    'gpt-4o',
    inputText,
    outputText,
    undefined, // messageId will be set later
    mainAgentDuration
  );

  const fragmentTitleGenerator = createAgent({
      name: "fragment-title-generator",
      description: "A fragment title generator",
      system: FRAGMENT_TITLE_PROMPT,
      model: openai({
        model: "gpt-4o",}),
      });
  const responseGenerator = createAgent({
      name: "response-generator",
      description: "A response generator",
      system: RESPONSE_PROMPT,
      model: openai({
        model: "gpt-4o",}),
      });

      // Track fragment title generation
      const titleStartTime = Date.now();
      console.log(`🚀 Starting fragment title generation`);
      const { output: fragmentTitleOutput } = await fragmentTitleGenerator.run(result.state.data.summary);
      const titleEndTime = Date.now();
      const titleDuration = titleEndTime - titleStartTime;

      // Track response generation
      const responseStartTime = Date.now();
      console.log(`🚀 Starting response generation`);
      const { output: responseOutput } = await responseGenerator.run(result.state.data.summary);
      const responseEndTime = Date.now();
      const responseDuration = responseEndTime - responseStartTime;

      // Track token usage for fragment title generator
      await trackAgentTokenUsage(
        event.data.projectId,
        'fragment-title-generator',
        'gpt-4o',
        result.state.data.summary || '',
        parseAgentOutput(fragmentTitleOutput),
        undefined, // messageId will be set later
        titleDuration
      );

      // Track token usage for response generator
      await trackAgentTokenUsage(
        event.data.projectId,
        'response-generator',
        'gpt-4o',
        result.state.data.summary || '',
        parseAgentOutput(responseOutput),
        undefined, // messageId will be set later
        responseDuration
      );


  const isError = !result.state.data.summary || Object.keys(result.state.data.files || {}).length === 0;

      const sandboxUrl = await step.run("get-sandbox-url", async () => {
        const sandbox = await getSandbox(sandboxId);
        const host = sandbox.getHost(3000);
        return `https://${host}`;
      });

      const savedMessage = await step.run("save-result", async () =>{
        if (isError){
          return await prisma.message.create({
            data:{
              projectId: event.data.projectId,
              content: "Something Went Wrong. Please try again.",
              role: "ASSISTANT",
              type: "ERROR",
            },
          });
        }
        return await prisma.message.create({
          data:{
            projectId: event.data.projectId,
            content: parseAgentOutput(responseOutput),
            role: "ASSISTANT",
            type: "RESULT",
            fragment: {
              create: {
                sandboxUrl: sandboxUrl,
                title: parseAgentOutput(fragmentTitleOutput),
                files: result.state.data.files,
              },

            },
          },
        })
      });

      // Update token usage records with the messageId
      await step.run("update-token-usage", async () => {
        if (savedMessage && !isError) {
          // Update token usage records with the messageId
          await prisma.tokenUsage.updateMany({
            where: {
              projectId: event.data.projectId,
              messageId: null,
              createdAt: {
                gte: new Date(mainAgentStartTime),
              },
            },
            data: {
              messageId: savedMessage.id,
            },
          });

          // Log total token usage summary
          const totalUsage = await prisma.tokenUsage.findMany({
            where: {
              messageId: savedMessage.id,
            },
          });

          const summary = totalUsage.reduce(
            (acc, usage) => ({
              totalInputTokens: acc.totalInputTokens + usage.inputTokens,
              totalOutputTokens: acc.totalOutputTokens + usage.outputTokens,
              totalTokens: acc.totalTokens + usage.totalTokens,
              totalCost: acc.totalCost + (usage.estimatedCost || 0),
            }),
            {
              totalInputTokens: 0,
              totalOutputTokens: 0,
              totalTokens: 0,
              totalCost: 0,
            }
          );

          console.log('\n' + '='.repeat(80));
          console.log('🎯 TOTAL TOKEN USAGE SUMMARY FOR REQUEST');
          console.log('='.repeat(80));
          console.log(`📊 Total Input Tokens: ${summary.totalInputTokens.toLocaleString()}`);
          console.log(`📤 Total Output Tokens: ${summary.totalOutputTokens.toLocaleString()}`);
          console.log(`📊 Total Tokens: ${summary.totalTokens.toLocaleString()}`);
          console.log(`💰 Total Estimated Cost: $${summary.totalCost.toFixed(4)}`);
          console.log(`🔢 Number of AI Calls: ${totalUsage.length}`);
          console.log(`📝 Message ID: ${savedMessage.id}`);
          console.log('='.repeat(80) + '\n');
        }
      });

      return { 
        url: sandboxUrl,
        title: "Fragment",
        files: result.state.data.files,
        summary: result.state.data.summary,
      };
    },
);
